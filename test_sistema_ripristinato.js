#!/usr/bin/env node
/**
 * Test completo per verificare che il sistema sia stato ripristinato correttamente
 */

const http = require('http');

function testEndpoint(url, description, expectedStatus = [200, 401]) {
    return new Promise((resolve) => {
        console.log(`🧪 ${description}`);
        
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || 80,
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            timeout: 8000,
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Sistema-Test'
            }
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                const isExpected = expectedStatus.includes(res.statusCode);
                const status = isExpected ? '✅' : '❌';
                
                console.log(`   ${status} Status: ${res.statusCode} ${isExpected ? '(ATTESO)' : '(INATTESO)'}`);
                
                if (res.statusCode === 500) {
                    console.log(`   🚨 ERRORE SERVER - Possibile problema modelli`);
                    console.log(`   📄 Risposta: ${data.substring(0, 200)}...`);
                } else if (res.statusCode === 401) {
                    console.log(`   ℹ️  Autenticazione richiesta (normale)`);
                } else if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        if (Array.isArray(jsonData)) {
                            console.log(`   📊 Dati ricevuti: ${jsonData.length} elementi`);
                        }
                    } catch (e) {
                        console.log(`   📄 Risposta non JSON`);
                    }
                }
                
                resolve({ 
                    success: isExpected, 
                    status: res.statusCode, 
                    hasServerError: res.statusCode === 500 
                });
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ Errore connessione: ${err.message}`);
            resolve({ success: false, error: err.message });
        });
        
        req.on('timeout', () => {
            console.log(`   ❌ Timeout`);
            req.destroy();
            resolve({ success: false, error: 'Timeout' });
        });
        
        req.end();
    });
}

async function main() {
    console.log('🔧 TEST SISTEMA RIPRISTINATO');
    console.log('Verifica che le correzioni ai modelli abbiano risolto i problemi');
    console.log('=' * 80);
    
    const tests = [
        {
            url: 'http://localhost:8001/api/parco-cavi/1',
            description: 'Endpoint Parco Cavi (principale)',
            expectedStatus: [200, 401] // 200 se autenticato, 401 se non autenticato
        },
        {
            url: 'http://localhost:8001/api/parco-cavi/1?disponibili_only=true',
            description: 'Endpoint Parco Cavi con filtro',
            expectedStatus: [200, 401]
        },
        {
            url: 'http://localhost:8001/api/cavi/1',
            description: 'Endpoint Cavi (verifica modello Cavo)',
            expectedStatus: [200, 401]
        },
        {
            url: 'http://localhost:8001/api/cantieri/',
            description: 'Endpoint Cantieri (controllo generale)',
            expectedStatus: [200, 401]
        },
        {
            url: 'http://localhost:8001/api/comande/cantiere/1',
            description: 'Endpoint Comande (verifica implementazione)',
            expectedStatus: [200, 401]
        }
    ];
    
    let passedTests = 0;
    let serverErrors = 0;
    
    console.log('\n📋 ESECUZIONE TEST:\n');
    
    for (const test of tests) {
        const result = await testEndpoint(test.url, test.description, test.expectedStatus);
        
        if (result.success) {
            passedTests++;
        }
        
        if (result.hasServerError) {
            serverErrors++;
        }
        
        console.log(''); // Riga vuota tra i test
    }
    
    console.log('=' * 80);
    console.log('📊 RISULTATI FINALI:');
    console.log('');
    console.log(`✅ Test superati: ${passedTests}/${tests.length}`);
    console.log(`🚨 Errori server (500): ${serverErrors}`);
    console.log('');
    
    if (serverErrors === 0) {
        console.log('🎉 SISTEMA RIPRISTINATO CON SUCCESSO!');
        console.log('');
        console.log('✅ Tutti gli endpoint funzionano correttamente');
        console.log('✅ Nessun errore di modelli SQLAlchemy');
        console.log('✅ Backend operativo');
        console.log('');
        console.log('🎯 PROSSIMI PASSI:');
        console.log('1. Rieffettua il login nel browser');
        console.log('2. Testa la pagina Parco Cavi');
        console.log('3. Verifica caricamento bobine');
        console.log('4. Testa inserimento metri posati');
        console.log('5. Verifica associazione bobine-comande');
    } else {
        console.log('❌ SISTEMA NON COMPLETAMENTE RIPRISTINATO');
        console.log('');
        console.log(`🚨 ${serverErrors} endpoint(s) con errori server`);
        console.log('');
        console.log('🔧 AZIONI NECESSARIE:');
        console.log('1. Verifica log backend per errori specifici');
        console.log('2. Controlla modelli SQLAlchemy');
        console.log('3. Verifica struttura database');
        console.log('4. Riavvia backend se necessario');
    }
    
    console.log('');
    console.log('💡 NOTA: Status 401 è normale se non autenticato');
    console.log('💡 NOTA: Status 200 indica endpoint funzionante con auth');
    console.log('💡 NOTA: Status 500 indica problema nel backend');
}

if (require.main === module) {
    main().catch(console.error);
}
