#!/usr/bin/env node
/**
 * Test per verificare il token di autenticazione e testare l'endpoint parco-cavi
 */

const http = require('http');

function testAuthenticatedEndpoint(url, token, description) {
    return new Promise((resolve) => {
        console.log(`🧪 Test: ${description}`);
        console.log(`   URL: ${url}`);
        console.log(`   Token: ${token ? token.substring(0, 20) + '...' : 'NESSUN TOKEN'}`);
        
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || 80,
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            timeout: 5000,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'User-Agent': 'Test-Script'
            }
        };
        
        // Aggiungi token se presente
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   Status: ${res.statusCode}`);
                
                if (res.statusCode === 200) {
                    console.log(`   ✅ ${description} - OK`);
                    try {
                        const jsonData = JSON.parse(data);
                        console.log(`   📊 Bobine trovate: ${jsonData.length}`);
                        if (jsonData.length > 0) {
                            console.log(`   📋 Prima bobina: ${JSON.stringify(jsonData[0], null, 2)}`);
                        }
                    } catch (e) {
                        console.log(`   📄 Risposta: ${data.substring(0, 200)}...`);
                    }
                } else if (res.statusCode === 401) {
                    console.log(`   ❌ ${description} - Non autenticato`);
                    console.log(`   📄 Risposta: ${data}`);
                } else {
                    console.log(`   ❌ ${description} - Status ${res.statusCode}`);
                    console.log(`   📄 Risposta: ${data.substring(0, 200)}...`);
                }
                
                resolve({
                    success: res.statusCode === 200,
                    status: res.statusCode,
                    data: data
                });
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ ${description} - Errore: ${err.message}`);
            resolve({
                success: false,
                error: err.message
            });
        });
        
        req.on('timeout', () => {
            console.log(`   ❌ ${description} - Timeout`);
            req.destroy();
            resolve({
                success: false,
                error: 'Timeout'
            });
        });
        
        req.end();
    });
}

async function testLogin() {
    return new Promise((resolve) => {
        console.log('🔐 Test Login Cantiere con credenziali di test');

        // Prova login cantiere invece di login standard
        const loginData = JSON.stringify({
            codice_univoco: "DUINO",
            password: "Taranto"
        });

        const options = {
            hostname: 'localhost',
            port: 8001,
            path: '/api/auth/login/cantiere',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(loginData)
            }
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   Status: ${res.statusCode}`);
                
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log(`   ✅ Login riuscito`);
                        console.log(`   🎫 Token: ${jsonData.access_token.substring(0, 20)}...`);
                        resolve({
                            success: true,
                            token: jsonData.access_token
                        });
                    } catch (e) {
                        console.log(`   ❌ Errore parsing risposta login`);
                        resolve({ success: false });
                    }
                } else {
                    console.log(`   ❌ Login fallito - Status ${res.statusCode}`);
                    console.log(`   📄 Risposta: ${data}`);
                    resolve({ success: false });
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ Errore login: ${err.message}`);
            resolve({ success: false });
        });
        
        req.write(loginData);
        req.end();
    });
}

async function main() {
    console.log('🚀 Test Autenticazione e Endpoint Parco Cavi');
    console.log('=' * 60);
    
    // Test 1: Login per ottenere token
    const loginResult = await testLogin();
    console.log('');
    
    if (!loginResult.success) {
        console.log('❌ Impossibile ottenere token di autenticazione');
        console.log('   Verificare che il backend sia configurato con utente admin/admin123');
        return;
    }
    
    // Test 2: Endpoint parco-cavi con token
    const tests = [
        {
            url: 'http://localhost:8001/api/parco-cavi/1',
            description: 'Parco Cavi Cantiere 1 (autenticato)'
        },
        {
            url: 'http://localhost:8001/api/parco-cavi/1?disponibili_only=true',
            description: 'Parco Cavi Disponibili Cantiere 1 (autenticato)'
        }
    ];
    
    let successCount = 0;
    
    for (const test of tests) {
        const result = await testAuthenticatedEndpoint(test.url, loginResult.token, test.description);
        if (result.success) {
            successCount++;
        }
        console.log('');
    }
    
    console.log('=' * 60);
    console.log(`📊 Risultati: ${successCount}/${tests.length} test autenticati passati`);
    
    if (successCount === tests.length) {
        console.log('✅ Tutti i test passati! L\'endpoint parco-cavi funziona correttamente.');
        console.log('\n💡 Il problema nel frontend potrebbe essere:');
        console.log('   1. Token non presente nel localStorage');
        console.log('   2. Token scaduto');
        console.log('   3. Problema nell\'interceptor axios');
        console.log('   4. ID cantiere non valido nella comanda');
    } else {
        console.log('❌ Alcuni test falliti anche con autenticazione.');
        console.log('   Verificare la configurazione dell\'endpoint parco-cavi.');
    }
}

if (require.main === module) {
    main().catch(console.error);
}
