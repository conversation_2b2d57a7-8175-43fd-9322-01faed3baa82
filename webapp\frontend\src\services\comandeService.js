import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const comandeService = {
  // Ottiene la lista delle comande di un cantiere
  getComande: async (cantiereId, params = {}) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, { params });
      return response.data;
    } catch (error) {
      console.error('Get comande error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una nuova comanda
  createComanda: async (comandaData) => {
    try {
      const response = await axiosInstance.post('/comande/', comandaData);
      return response.data;
    } catch (error) {
      console.error('Create comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i dettagli di una comanda
  getDettagliComanda: async (codiceComanda) => {
    try {
      const response = await axiosInstance.get(`/comande/${codiceComanda}`);
      return response.data;
    } catch (error) {
      console.error('Get dettagli comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Assegna cavi a una comanda
  assegnaCavi: async (codiceComanda, listaIdCavi) => {
    try {
      const response = await axiosInstance.post(`/comande/${codiceComanda}/assegna-cavi`, {
        lista_id_cavi: listaIdCavi
      });
      return response.data;
    } catch (error) {
      console.error('Assegna cavi error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di posa
  aggiornaDatiPosa: async (codiceComanda, datiPosa) => {
    try {
      const response = await axiosInstance.put(`/comande/${codiceComanda}/dati-posa`, {
        dati_posa: datiPosa
      });
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati posa error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di collegamento
  aggiornaDatiCollegamento: async (codiceComanda, datiCollegamento) => {
    try {
      const response = await axiosInstance.put(`/comande/${codiceComanda}/dati-collegamento`, {
        dati_collegamento: datiCollegamento
      });
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati collegamento error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Cambia lo stato di una comanda
  cambiaStato: async (codiceComanda, nuovoStato) => {
    try {
      const response = await axiosInstance.put(`/comande/${codiceComanda}/stato`, {
        nuovo_stato: nuovoStato
      });
      return response.data;
    } catch (error) {
      console.error('Cambia stato error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene le statistiche delle comande per un cantiere
  getStatisticheComande: async (cantiereId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}/statistiche`);
      return response.data;
    } catch (error) {
      console.error('Get statistiche comande error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i cavi assegnati a una comanda
  getCaviComanda: async (codiceComanda) => {
    try {
      const response = await axiosInstance.get(`/comande/${codiceComanda}/cavi`);
      return response.data;
    } catch (error) {
      console.error('Get cavi comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di posa per una comanda
  aggiornaDatiPosa: async (codiceComanda, datiPosa) => {
    try {
      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-posa`, datiPosa);
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati posa error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di posa con associazioni bobine
  aggiornaDatiPosaConBobine: async (codiceComanda, datiPosa) => {
    try {
      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-posa-bobine`, datiPosa);
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati posa con bobine error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di collegamento per una comanda
  aggiornaDatiCollegamento: async (codiceComanda, datiCollegamento) => {
    try {
      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-collegamento`, datiCollegamento);
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati collegamento error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di certificazione per una comanda
  aggiornaDatiCertificazione: async (codiceComanda, datiCertificazione) => {
    try {
      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-certificazione`, datiCertificazione);
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati certificazione error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene le comande per un responsabile specifico
  getComandeByResponsabile: async (cantiereId, nomeResponsabile) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, {
        params: { responsabile: nomeResponsabile }
      });
      return response.data;
    } catch (error) {
      console.error('Get comande by responsabile error:', error);
      // Non lanciare errore, ritorna array vuoto
      return [];
    }
  },

  // Aggiorna una comanda esistente (solo campi modificabili)
  updateComanda: async (codiceComanda, comandaData) => {
    try {
      console.log('🔄 Aggiornamento comanda:', { codiceComanda, comandaData });

      const response = await axiosInstance.put(`/comande/${codiceComanda}`, comandaData);

      console.log('✅ Comanda aggiornata:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Errore aggiornamento comanda:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina una comanda
  deleteComanda: async (codiceComanda) => {
    try {
      const response = await axiosInstance.delete(`/comande/${codiceComanda}`);
      return response.data;
    } catch (error) {
      console.error('Delete comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Assegna una comanda a un cavo
  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {
        id_cavo: idCavo
      });
      return response.data;
    } catch (error) {
      console.error('Assign comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Genera PDF di una comanda
  printComanda: async (cantiereId, idComanda) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);
      return response.data;
    } catch (error) {
      console.error('Print comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i cavi disponibili per un tipo di comanda
  getCaviDisponibili: async (cantiereId, tipoComanda) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}/cavi-disponibili`, {
        params: { tipo_comanda: tipoComanda }
      });
      return response.data;
    } catch (error) {
      console.error('Get cavi disponibili error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una comanda con cavi pre-selezionati
  createComandaConCavi: async (cantiereId, comandaData, listaIdCavi) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Prepara il payload completo con i dati della comanda e la lista dei cavi
      const payload = {
        ...comandaData,
        lista_id_cavi: listaIdCavi
      };

      console.log('🚀 Creazione comanda con cavi:', {
        cantiereId: cantiereIdNum,
        tipoComanda: comandaData.tipo_comanda,
        responsabile: comandaData.responsabile,
        numeroCavi: listaIdCavi.length,
        cavi: listaIdCavi
      });

      const response = await axiosInstance.post(`/comande/cantiere/${cantiereIdNum}/crea-con-cavi?${listaIdCavi.map(id => `lista_id_cavi=${encodeURIComponent(id)}`).join('&')}`, comandaData);

      console.log('✅ Comanda creata con successo:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Errore nella creazione comanda con cavi:', error);
      console.error('Dettagli errore:', error.response?.data);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di certificazione
  aggiornaDatiCertificazione: async (codiceComanda, datiCertificazione) => {
    try {
      const response = await axiosInstance.put(`/comande/${codiceComanda}/certificazione`, datiCertificazione);
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati certificazione error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Assegna cavi a una comanda esistente
  assegnaCaviAComanda: async (codiceComanda, listaIdCavi) => {
    try {
      console.log('🔄 Assegnazione cavi a comanda:', { codiceComanda, listaIdCavi });

      const response = await axiosInstance.post(`/comande/${codiceComanda}/assegna-cavi`, {
        lista_id_cavi: listaIdCavi
      });

      console.log('✅ Cavi assegnati con successo:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Errore nell\'assegnazione cavi:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Rimuove un cavo da una comanda
  rimuoviCavoDaComanda: async (codiceComanda, idCavo) => {
    try {
      console.log('🔄 Rimozione cavo da comanda:', { codiceComanda, idCavo });

      const response = await axiosInstance.delete(`/comande/${codiceComanda}/cavi/${idCavo}`);

      console.log('✅ Cavo rimosso con successo:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Errore nella rimozione cavo:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default comandeService;
