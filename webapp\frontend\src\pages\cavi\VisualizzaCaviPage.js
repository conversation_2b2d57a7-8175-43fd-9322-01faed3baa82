import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  IconButton,
  Chip,
  CircularProgress,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import {
  Cable as CableIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Link as LinkIcon,
  LinkOff as LinkOffIcon,
  Timeline as TimelineIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  SelectAll as SelectAllIcon,
  ContentCopy as CopyIcon,
  Settings as SettingsIcon,
  Verified as VerifiedIcon,
  Build as BuildIcon,
  Assignment as AssignmentIcon,
  Remove as RemoveIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useGlobalContext } from '../../context/GlobalContext';
// import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti'; // OBSOLETO: Componente eliminato
import caviService from '../../services/caviService';
import parcoCaviService from '../../services/parcoCaviService';
import CavoForm from '../../components/cavi/CavoForm';
import { normalizeInstallationStatus } from '../../utils/validationUtils';
import CaviFilterableTable from '../../components/cavi/CaviFilterableTable';
import InserisciMetriDialogCompleto from '../../components/cavi/InserisciMetriDialogCompleto';
import ModificaBobinaDialogCompleto from '../../components/cavi/ModificaBobinaDialogCompleto';
import CollegamentiCavo from '../../components/cavi/CollegamentiCavo';
import CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';
import CreaComandaConCavi from '../../components/comande/CreaComandaConCavi';
// import comandeValidationService from '../../services/comandeValidationService';

import './CaviPage.css';

const VisualizzaCaviPage = () => {
  const { isImpersonating, user } = useAuth();
  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();
  const navigate = useNavigate();
  const [cantiereId, setCantiereId] = useState(null);
  const [cantiereName, setCantiereName] = useState('');
  const [caviAttivi, setCaviAttivi] = useState([]);
  const [caviSpare, setCaviSpare] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // Stato per le notifiche
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  // Rimosso stato viewMode

  // Stato per il dialogo dei dettagli del cavo
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  // Stati per la selezione dei cavi
  const [selectionEnabled, setSelectionEnabled] = useState(false);
  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);
  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);

  // Stati per i dialoghi di azione sui pulsanti stato
  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({
    open: false,
    cavo: null,
    loading: false
  });

  const [collegamentiDialog, setCollegamentiDialog] = useState({
    open: false,
    cavo: null,
    loading: false
  });
  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({
    open: false,
    cavo: null,
    loading: false
  });

  // Ref per il componente CertificazioneCavi
  const certificazioneRef = useRef(null);

  // Stati per il dialog di creazione comande multiple
  const [createCommandDialog, setCreateCommandDialog] = useState({
    open: false,
    tipoComanda: '',
    caviSelezionati: [],
    loading: false
  });



  // Stati per statistiche avanzate
  const [statistics, setStatistics] = useState({
    totaleCavi: 0,
    caviInstallati: 0,
    caviDaInstallare: 0,
    caviInCorso: 0,
    caviCollegati: 0,
    caviNonCollegati: 0,
    caviCertificati: 0,
    caviNonCertificati: 0,
    percentualeInstallazione: 0,
    percentualeCollegamento: 0,
    percentualeCertificazione: 0,
    iap: 0, // Indice di Avanzamento Ponderato
    metriTotali: 0,
    metriInstallati: 0,
    metriRimanenti: 0
  });





  // Stato per la revisione corrente (solo per visualizzazione nelle statistiche)
  const [revisioneCorrente, setRevisioneCorrente] = useState('');

  // Rimosso stato per il debug

  // Funzione per calcolare l'Indice di Avanzamento Ponderato (IAP)
  const calculateIAP = (nTot, nInst, nColl, nCert) => {
    // Pesi per le fasi del progetto
    const Wp = 2.0;  // Peso fase Posa
    const Wc = 1.5;  // Peso fase Collegamento
    const Wz = 0.5;  // Peso fase Certificazione

    // Se non ci sono cavi, ritorna 0
    if (nTot === 0) return 0;

    // Calcolo del numeratore (Sforzo Completato)
    const sforzoSoloInstallati = (nInst - nColl) * Wp;
    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc);
    const sforzoCertificati = nCert * (Wp + Wc + Wz);
    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati;

    // Calcolo del denominatore (Sforzo Massimo Previsto)
    const denominatore = nTot * (Wp + Wc + Wz);

    // Calcolo finale dell'IAP in percentuale
    const iap = (numeratore / denominatore) * 100;

    console.log('Calcolo IAP:', {
      nTot, nInst, nColl, nCert,
      pesi: { Wp, Wc, Wz },
      sforzoSoloInstallati,
      sforzoSoloCollegati,
      sforzoCertificati,
      numeratore,
      denominatore,
      iap: Math.round(iap * 100) / 100
    });

    return Math.round(iap * 100) / 100; // Arrotonda a 2 decimali
  };

  // Funzione per calcolare le statistiche avanzate
  const calculateStatistics = (caviAttiviData, caviSpareData) => {
    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];

    if (tuttiCavi.length === 0) {
      console.log('Nessun cavo disponibile per il calcolo delle statistiche');
      return;
    }

    console.log('Calcolo statistiche con dati:', {
      caviAttivi: caviAttiviData?.length || 0,
      caviSpare: caviSpareData?.length || 0,
      totale: tuttiCavi.length
    });

    const totaleCavi = tuttiCavi.length;

    // Calcola stati di installazione
    const caviInstallati = tuttiCavi.filter(cavo =>
      cavo.stato_installazione === 'Installato' ||
      cavo.stato_installazione === 'INSTALLATO' ||
      cavo.stato_installazione === 'POSATO'
    ).length;

    const caviDaInstallare = tuttiCavi.filter(cavo =>
      cavo.stato_installazione === 'Da installare' ||
      cavo.stato_installazione === 'DA_INSTALLARE'
    ).length;

    const caviInCorso = tuttiCavi.filter(cavo =>
      cavo.stato_installazione === 'In corso' ||
      cavo.stato_installazione === 'IN_CORSO'
    ).length;

    // Calcola stati di collegamento
    const caviCollegati = tuttiCavi.filter(cavo =>
      cavo.collegamenti === 3 &&
      cavo.responsabile_partenza &&
      cavo.responsabile_arrivo
    ).length;

    const caviNonCollegati = totaleCavi - caviCollegati;

    // Calcola certificazioni basandosi sul campo 'certificato' dei cavi
    const caviCertificati = tuttiCavi.filter(cavo => cavo.certificato === true || cavo.certificato === 'true').length;

    // Calcola l'Indice di Avanzamento Ponderato (IAP)
    const iap = calculateIAP(totaleCavi, caviInstallati, caviCollegati, caviCertificati);

    // Calcola percentuali tradizionali per confronto
    const percentualeInstallazione = iap; // Sostituito con IAP
    const percentualeCollegamento = totaleCavi > 0 ? Math.round((caviCollegati / totaleCavi) * 100) : 0;

    // Calcola metri
    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);
    const metriInstallati = tuttiCavi
      .filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO')
      .reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);
    const metriRimanenti = metriTotali - metriInstallati;
    const caviNonCertificati = totaleCavi - caviCertificati;
    const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;

    const newStatistics = {
      totaleCavi,
      caviInstallati,
      caviDaInstallare,
      caviInCorso,
      caviCollegati,
      caviNonCollegati,
      caviCertificati,
      caviNonCertificati,
      percentualeInstallazione,
      percentualeCollegamento,
      percentualeCertificazione,
      iap, // Indice di Avanzamento Ponderato
      metriTotali: Math.round(metriTotali),
      metriInstallati: Math.round(metriInstallati),
      metriRimanenti: Math.round(metriRimanenti)
    };

    console.log('Nuove statistiche calcolate:', newStatistics);
    setStatistics(newStatistics);
  };

  // Funzione per caricare gli stati di installazione disponibili
  const loadStatiInstallazione = () => {
    // Usa i valori dell'enum StatoInstallazione
    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);
  };

  // Funzione per caricare solo la revisione corrente (per le statistiche)
  const loadRevisioneCorrente = async (cantiereIdToUse) => {
    try {
      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);
      // Il servizio restituisce un oggetto con la proprietà revisione_corrente
      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);
    } catch (error) {
      console.error('Errore nel caricamento della revisione corrente:', error);
      // Non bloccare l'applicazione se la revisione non è disponibile
    }
  };





  // Stato per filtri e ordinamento
  const [filters, setFilters] = useState({
    stato_installazione: '',
    tipologia: '',
    sort_by: '',
    sort_order: 'asc'
  });

  // Opzioni per i filtri
  const [statiInstallazione, setStatiInstallazione] = useState([]);
  const [tipologieCavi, setTipologieCavi] = useState([]);

  // Rimossa funzione di debug

  // Funzione per caricare i cavi
  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento
  const fetchCavi = async (silentLoading = false) => {
    try {
      if (!silentLoading) {
        setLoading(true);
      }
      console.log('Caricamento cavi per cantiere:', cantiereId);

      // Verifica che cantiereId sia valido
      if (!cantiereId) {
        console.error('fetchCavi: cantiereId non valido:', cantiereId);
        setError('ID cantiere non valido o mancante. Ricarica la pagina.');
        setLoading(false);
        return;
      }

      // Recupera il cantiereId dal localStorage come fallback
      let cantiereIdToUse = cantiereId;
      if (!cantiereIdToUse) {
        cantiereIdToUse = localStorage.getItem('selectedCantiereId');
        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);
        if (!cantiereIdToUse) {
          console.error('Impossibile trovare un ID cantiere valido');
          setError('ID cantiere non trovato. Ricarica la pagina.');
          setLoading(false);
          return;
        }
      }

      // Carica i cavi attivi
      console.log('Caricamento cavi attivi (tipo_cavo=0)...');
      let attivi = [];
      try {
        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);
        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);
      } catch (attiviError) {
        console.error('Errore nel caricamento dei cavi attivi:', attiviError);
        // Continua con un array vuoto
        attivi = [];
      }

      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi
      if (attivi && attivi.length > 0) {
        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);
        if (caviSpareTraAttivi.length > 0) {
          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);
        }
      }

      setCaviAttivi(attivi || []);

      // Carica i cavi SPARE con la nuova funzione dedicata
      let spare = [];
      try {
        console.log('Caricamento cavi SPARE con funzione dedicata...');
        spare = await caviService.getCaviSpare(cantiereIdToUse);
        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);
        if (spare && spare.length > 0) {
          console.log('Primo cavo SPARE:', spare[0]);
        }
      } catch (spareError) {
        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);
        // Se fallisce, prova con il metodo standard
        try {
          console.log('Tentativo con metodo standard...');
          spare = await caviService.getCavi(cantiereIdToUse, 3);
          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);
        } catch (standardError) {
          console.error('Errore anche con metodo standard:', standardError);
          // Continua con un array vuoto
          spare = [];
        }
      }
      setCaviSpare(spare || []);



      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti
      setError('');
    } catch (error) {
      console.error('Errore generale nel caricamento dei cavi:', error);
      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);

      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste
      setTimeout(() => {
        // Verifica se siamo ancora in errore
        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {
          console.log('Errore persistente, tentativo di ricaricamento della pagina...');
          window.location.reload();
        }
      }, 5000); // 5 secondi di ritardo
    } finally {
      if (!silentLoading) {
        setLoading(false);
      }
    }
  };

  // Carica i dati del cantiere e dei cavi
  useEffect(() => {
    // Carica gli stati di installazione all'avvio
    loadStatiInstallazione();

    const fetchData = async () => {
      try {
        console.log('Inizializzazione VisualizzaCaviPage...');

        // Verifica che l'utente sia autenticato
        const token = localStorage.getItem('token');
        console.log('Token presente:', !!token);
        if (!token) {
          setError('Sessione scaduta. Effettua nuovamente il login.');
          setLoading(false);
          return;
        }

        // Recupera l'ID del cantiere selezionato dal localStorage
        let selectedCantiereId = localStorage.getItem('selectedCantiereId');
        let selectedCantiereName = localStorage.getItem('selectedCantiereName');

        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });
        console.log('Dati utente:', user);

        // Stampa tutti i dati nel localStorage per debug
        console.log('DEBUG - Tutti i dati nel localStorage:');
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          console.log(`${key}: ${localStorage.getItem(key)}`);
        }

        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT
        if (user?.role === 'cantieri_user') {
          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');

          // Verifica se l'utente ha un ID cantiere nei dati utente
          if (user.cantiere_id) {
            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);
            selectedCantiereId = user.cantiere_id.toString();
            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;

            // Salva l'ID e il nome del cantiere nel localStorage
            localStorage.setItem('selectedCantiereId', selectedCantiereId);
            localStorage.setItem('selectedCantiereName', selectedCantiereName);
            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);
          } else {
            // Tentativo di recupero dal token JWT
            try {
              console.log('Tentativo di decodifica del token JWT per recuperare l\'ID cantiere');
              const token = localStorage.getItem('token');
              if (token) {
                // Decodifica il token JWT (senza verifica della firma)
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));

                const payload = JSON.parse(jsonPayload);
                console.log('Payload del token JWT:', payload);

                if (payload.cantiere_id) {
                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);
                  selectedCantiereId = payload.cantiere_id.toString();
                  // Usa un nome generico se non disponibile
                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;

                  // Salva l'ID e il nome del cantiere nel localStorage
                  localStorage.setItem('selectedCantiereId', selectedCantiereId);
                  localStorage.setItem('selectedCantiereName', selectedCantiereName);
                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);
                }
              }
            } catch (e) {
              console.error('Errore durante la decodifica del token JWT:', e);
            }
          }
        }

        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug
        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {
          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');
          // Usa il primo cantiere disponibile (questo è solo per debug)
          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database
          selectedCantiereName = 'Cantiere Debug';

          // Salva l'ID e il nome del cantiere nel localStorage
          localStorage.setItem('selectedCantiereId', selectedCantiereId);
          localStorage.setItem('selectedCantiereName', selectedCantiereName);
          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);
        }

        // Verifica finale
        if (!selectedCantiereId) {
          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');
          setLoading(false);
          return;
        }

        // Verifica che l'ID del cantiere sia un numero valido
        const cantiereIdNum = parseInt(selectedCantiereId, 10);
        console.log('ID cantiere convertito a numero:', cantiereIdNum);
        if (isNaN(cantiereIdNum)) {
          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);
          setLoading(false);
          return;
        }

        // Usa il numero convertito, non la stringa
        setCantiereId(cantiereIdNum);
        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);

        // Carica la revisione corrente per le statistiche
        await loadRevisioneCorrente(cantiereIdNum);





        // Carica i cavi attivi con gestione degli errori migliorata
        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);
        try {
          // Imposta un timeout per evitare che la richiesta rimanga bloccata
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi
          });

          // Esegui la richiesta con un timeout di sicurezza e applica i filtri
          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);
          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);
          const attivi = await Promise.race([caviPromise, timeoutPromise]);

          console.log('Cavi attivi caricati:', attivi);
          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);
          if (attivi && attivi.length > 0) {
            console.log('Primo cavo attivo:', attivi[0]);
          } else {
            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);
          }
          setCaviAttivi(attivi || []);

          // Calcola le statistiche dopo aver caricato i cavi attivi
          calculateStatistics(attivi || [], caviSpare);
        } catch (caviError) {
          console.error('Errore nel caricamento dei cavi attivi:', caviError);
          console.error('Dettagli errore cavi attivi:', {
            message: caviError.message,
            status: caviError.status,
            data: caviError.data,
            stack: caviError.stack,
            code: caviError.code,
            name: caviError.name,
            response: caviError.response ? {
              status: caviError.response.status,
              statusText: caviError.response.statusText,
              data: caviError.response.data
            } : 'No response'
          });

          // Non interrompere il flusso, continua con i cavi spare
          setCaviAttivi([]);
          console.warn('Continuazione del flusso dopo errore nei cavi attivi');

          // Aggiungi un messaggio di errore visibile all'utente
          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);
        }

        // Carica i cavi spare con gestione degli errori migliorata
        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);
        try {
          // Imposta un timeout per evitare che la richiesta rimanga bloccata
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi
          });

          // Esegui la richiesta con un timeout di sicurezza
          console.log('Iniziando chiamata API per cavi spare...');
          // Non applichiamo i filtri ai cavi spare, solo agli attivi
          const sparePromise = caviService.getCavi(cantiereIdNum, 3);
          const spare = await Promise.race([sparePromise, timeoutPromise]);

          console.log('Cavi spare caricati:', spare);
          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);
          if (spare && spare.length > 0) {
            console.log('Primo cavo spare:', spare[0]);
          } else {
            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);
          }
          setCaviSpare(spare || []);

          // Calcola le statistiche dopo aver caricato i cavi spare
          calculateStatistics(caviAttivi, spare || []);
        } catch (spareError) {
          console.error('Errore nel caricamento dei cavi spare:', spareError);
          console.error('Dettagli errore cavi spare:', {
            message: spareError.message,
            status: spareError.status,
            data: spareError.data,
            stack: spareError.stack,
            code: spareError.code,
            name: spareError.name,
            response: spareError.response ? {
              status: spareError.response.status,
              statusText: spareError.response.statusText,
              data: spareError.response.data
            } : 'No response'
          });

          // Non interrompere il flusso, imposta un array vuoto
          setCaviSpare([]);

          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi
          if (!error) {
            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);
          }
        }

        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base
        setLoading(false);

      } catch (err) {
        console.error('Errore nel caricamento dei cavi:', err);
        console.error('Dettagli errore generale:', {
          message: err.message,
          status: err.status || err.response?.status,
          data: err.data || err.response?.data,
          stack: err.stack
        });

        // Estrai il messaggio di errore dettagliato
        let errorMessage = 'Errore sconosciuto';

        if (err.message && err.message.includes('ID cantiere non valido')) {
          errorMessage = err.message;
        } else if (err.status === 401 || err.status === 403 ||
                  err.response?.status === 401 || err.response?.status === 403) {
          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';
        } else if (err.response?.data?.detail) {
          // Estrai il messaggio di errore dettagliato dall'API
          errorMessage = `Errore API: ${err.response.data.detail}`;
        } else if (err.code === 'ERR_NETWORK') {
          // Errore di rete
          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';
        } else if (err.message) {
          errorMessage = err.message;
        }

        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);

        // Imposta array vuoti per evitare errori di rendering
        setCaviAttivi([]);
        setCaviSpare([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]); // Ricarica i dati quando cambiano i filtri

  // I filtri sono ora gestiti dal componente CaviFilterableTable

  // Funzione per aprire il dialogo dei dettagli del cavo
  const handleOpenDetails = (cavo) => {
    setSelectedCavo(cavo);
    setDetailsDialogOpen(true);
  };

  // Funzione per chiudere il dialogo dei dettagli del cavo
  const handleCloseDetails = () => {
    setDetailsDialogOpen(false);
    setSelectedCavo(null);
  };

  // Funzione per chiudere la notifica
  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Funzione per mostrare una notifica
  const showNotification = (message, severity = 'success') => {
    setNotification({ open: true, message, severity });
  };

  // Funzioni per gestire la selezione dei cavi
  const handleSelectionToggle = () => {
    setSelectionEnabled(!selectionEnabled);
    // Pulisci le selezioni quando si disabilita la modalità selezione
    if (selectionEnabled) {
      setSelectedCaviAttivi([]);
      setSelectedCaviSpare([]);
    }
  };

  const handleCaviAttiviSelectionChange = (selectedIds) => {
    setSelectedCaviAttivi(selectedIds);
  };

  const handleCaviSpareSelectionChange = (selectedIds) => {
    setSelectedCaviSpare(selectedIds);
  };

  // Funzione per ottenere tutti i cavi selezionati
  const getAllSelectedCavi = () => {
    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));
    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));
    return [...selectedAttiviCavi, ...selectedSpareCavi];
  };

  // Funzione per ottenere il conteggio totale dei cavi selezionati
  const getTotalSelectedCount = () => {
    return selectedCaviAttivi.length + selectedCaviSpare.length;
  };

  // Funzioni per gestire le azioni del menu contestuale
  const handleContextMenuAction = (cavo, action) => {
    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);

    switch (action) {
      case 'view_details':
        handleOpenDetails(cavo);
        break;
      case 'edit':
        // Funzionalità di modifica cavo non ancora implementata
        showNotification('Funzionalità di modifica cavo in sviluppo', 'info');
        break;
      case 'delete':
        // Funzionalità di eliminazione cavo non ancora implementata
        showNotification('Funzionalità di eliminazione cavo in sviluppo', 'info');
        break;
      case 'select':
        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {
          // È un cavo attivo
          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);
          if (isSelected) {
            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));
          } else {
            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);
          }
        } else {
          // È un cavo spare
          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);
          if (isSelected) {
            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));
          } else {
            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);
          }
        }
        // Abilita automaticamente la modalità selezione se non è già attiva
        if (!selectionEnabled) {
          setSelectionEnabled(true);
        }
        break;
      case 'copy_id':
        const totalSelectedCount = getTotalSelectedCount();
        if (totalSelectedCount > 1) {
          // Copia tutti gli ID dei cavi selezionati
          const allSelectedCavi = getAllSelectedCavi();
          const allIds = allSelectedCavi.map(c => c.id_cavo).join(', ');
          navigator.clipboard.writeText(allIds);
          showNotification(`${totalSelectedCount} IDs cavi copiati negli appunti`, 'success');
        } else {
          navigator.clipboard.writeText(cavo.id_cavo);
          showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');
        }
        break;
      case 'copy_details':
        const totalSelected = getTotalSelectedCount();
        if (totalSelected > 1) {
          // Copia dettagli di tutti i cavi selezionati
          const allSelectedCavi = getAllSelectedCavi();
          const allDetails = allSelectedCavi.map(c =>
            `ID: ${c.id_cavo}\nTipologia: ${c.tipologia}\nSezione: ${c.sezione}\nMetri: ${c.metri_teorici}`
          ).join('\n\n');
          navigator.clipboard.writeText(allDetails);
          showNotification(`Dettagli di ${totalSelected} cavi copiati negli appunti`, 'success');
        } else {
          const details = `ID: ${cavo.id_cavo}\nTipologia: ${cavo.tipologia}\nSezione: ${cavo.sezione}\nMetri: ${cavo.metri_teorici}`;
          navigator.clipboard.writeText(details);
          showNotification('Dettagli cavo copiati negli appunti', 'success');
        }
        break;
      case 'add_new':
        // Funzionalità di aggiunta cavo non ancora implementata
        showNotification('Funzionalità di aggiunta cavo in sviluppo', 'info');
        break;
      // Nuove azioni per la creazione di comande multiple
      case 'create_command_posa':
        handleCreateMultipleCommand('POSA');
        break;
      case 'create_command_collegamento_partenza':
        handleCreateMultipleCommand('COLLEGAMENTO_PARTENZA');
        break;
      case 'create_command_collegamento_arrivo':
        handleCreateMultipleCommand('COLLEGAMENTO_ARRIVO');
        break;
      case 'create_command_certificazione':
        handleCreateMultipleCommand('CERTIFICAZIONE');
        break;
      // Nuove azioni per gestione comande singolo cavo
      case 'add_to_command':
        handleAddToCommand(cavo);
        break;
      case 'remove_from_command':
        handleRemoveFromCommand(cavo);
        break;
      // Nuove azioni per gestione comande multiple
      case 'add_multiple_to_command':
        handleAddMultipleToCommand();
        break;
      case 'remove_multiple_from_commands':
        handleRemoveMultipleFromCommands();
        break;
      default:
        console.warn('Azione non riconosciuta:', action);
    }
  };

  // Funzione per controllare rapidamente la validazione di un tipo di comanda
  const getQuickValidationStatus = (tipoComanda) => {
    // Temporaneamente disabilitato per debug - restituisce sempre valido
    return { valid: true, issues: 0, errors: 0, warnings: 0 };
  };

  // Funzione per gestire la creazione di comande multiple
  const handleCreateMultipleCommand = (tipoComanda) => {
    const allSelectedCavi = getAllSelectedCavi();

    if (allSelectedCavi.length === 0) {
      showNotification('Nessun cavo selezionato per la creazione della comanda', 'warning');
      return;
    }

    console.log(`Creazione comanda ${tipoComanda} per ${allSelectedCavi.length} cavi:`, allSelectedCavi.map(c => c.id_cavo));

    // Apri il dialog di creazione comanda con i cavi preselezionati
    setCreateCommandDialog({
      open: true,
      tipoComanda: tipoComanda,
      caviSelezionati: allSelectedCavi,
      loading: false
    });
  };

  // Funzione per chiudere il dialog di creazione comande
  const handleCloseCreateCommand = () => {
    setCreateCommandDialog({
      open: false,
      tipoComanda: '',
      caviSelezionati: [],
      loading: false
    });
  };

  // Funzione per gestire il successo della creazione comanda
  const handleCreateCommandSuccess = (response) => {
    showNotification(`Comanda ${response.codice_comanda} creata con successo!`, 'success');

    // Deseleziona tutti i cavi
    setSelectedCaviAttivi([]);
    setSelectedCaviSpare([]);

    // Chiudi il dialog
    handleCloseCreateCommand();

    // Ricarica i dati per aggiornare lo stato
    setTimeout(() => fetchCavi(true), 500);
  };

  // Funzione per gestire gli errori nella creazione comanda
  const handleCreateCommandError = (error) => {
    console.error('Errore nella creazione della comanda:', error);
    showNotification('Errore nella creazione della comanda', 'error');
  };

  // Stati per i dialog di gestione comande singolo cavo
  const [addToCommandDialog, setAddToCommandDialog] = useState({
    open: false,
    cavo: null,
    comande: [],
    loading: false
  });

  const [removeFromCommandDialog, setRemoveFromCommandDialog] = useState({
    open: false,
    cavo: null,
    comandaCorrente: null,
    loading: false
  });

  // Funzione per aggiungere un cavo a una comanda esistente
  const handleAddToCommand = async (cavo) => {
    console.log('🔄 Aggiunta cavo a comanda:', cavo.id_cavo);
    console.log('🏗️ Cantiere ID:', cantiereId);

    try {
      console.log('📋 Apertura dialog aggiunta comanda...');
      setAddToCommandDialog({
        open: true,
        cavo: cavo,
        comande: [],
        loading: true
      });

      // Carica le comande disponibili per il cantiere
      console.log('📡 Caricamento comande dal servizio...');
      const comandeService = await import('../../services/comandeService');
      const comande = await comandeService.default.getComande(cantiereId);
      console.log('📋 Comande ricevute:', comande);

      // Gestisci diversi formati di risposta
      let comandeArray = [];
      if (Array.isArray(comande)) {
        comandeArray = comande;
      } else if (comande && Array.isArray(comande.comande)) {
        comandeArray = comande.comande;
      } else if (comande && Array.isArray(comande.data)) {
        comandeArray = comande.data;
      }

      console.log('📋 Array comande processato:', comandeArray);

      // Filtra le comande che possono accettare questo cavo
      const comandeDisponibili = comandeArray.filter(comanda => {
        // Escludi comande completate o cancellate
        return comanda.stato !== 'COMPLETATA' && comanda.stato !== 'CANCELLATA';
      });

      console.log('📋 Comande disponibili filtrate:', comandeDisponibili);

      setAddToCommandDialog({
        open: true,
        cavo: cavo,
        comande: comandeDisponibili,
        loading: false
      });

    } catch (error) {
      console.error('❌ Errore nel caricamento delle comande:', error);
      showNotification('Errore nel caricamento delle comande disponibili', 'error');
      setAddToCommandDialog({
        open: false,
        cavo: null,
        comande: [],
        loading: false
      });
    }
  };

  // Funzione per rimuovere un cavo da una comanda
  const handleRemoveFromCommand = (cavo) => {
    console.log('🔄 Rimozione cavo da comanda:', cavo.id_cavo);

    // Determina la comanda corrente del cavo
    const comandaCorrente = cavo.comanda_posa || cavo.comanda_partenza ||
                           cavo.comanda_arrivo || cavo.comanda_certificazione;

    if (!comandaCorrente) {
      showNotification('Il cavo non è assegnato a nessuna comanda', 'warning');
      return;
    }

    setRemoveFromCommandDialog({
      open: true,
      cavo: cavo,
      comandaCorrente: comandaCorrente,
      loading: false
    });
  };

  // Funzione per confermare l'aggiunta del cavo/cavi alla comanda
  const handleConfirmAddToCommand = async (comandaSelezionata) => {
    try {
      setAddToCommandDialog(prev => ({ ...prev, loading: true }));

      const comandeService = await import('../../services/comandeService');

      if (addToCommandDialog.isMultiple && addToCommandDialog.selectedCavi) {
        // Modalità multipla: aggiungi tutti i cavi selezionati
        const listaIdCavi = addToCommandDialog.selectedCavi.map(cavo => cavo.id_cavo);

        await comandeService.default.assegnaCaviAComanda(
          comandaSelezionata.codice_comanda,
          listaIdCavi
        );

        showNotification(
          `${listaIdCavi.length} cavi aggiunti alla comanda ${comandaSelezionata.codice_comanda}`,
          'success'
        );

        // Deseleziona tutti i cavi
        setSelectedCaviAttivi([]);
        setSelectedCaviSpare([]);

      } else {
        // Modalità singola: aggiungi solo il cavo corrente
        await comandeService.default.assegnaCaviAComanda(
          comandaSelezionata.codice_comanda,
          [addToCommandDialog.cavo.id_cavo]
        );

        showNotification(
          `Cavo ${addToCommandDialog.cavo.id_cavo} aggiunto alla comanda ${comandaSelezionata.codice_comanda}`,
          'success'
        );
      }

      // Chiudi il dialog
      setAddToCommandDialog({
        open: false,
        cavo: null,
        comande: [],
        loading: false,
        isMultiple: false,
        selectedCavi: []
      });

      // Ricarica i dati
      setTimeout(() => fetchCavi(true), 500);

    } catch (error) {
      console.error('Errore nell\'aggiunta del cavo alla comanda:', error);
      showNotification('Errore nell\'aggiunta del cavo alla comanda', 'error');
      setAddToCommandDialog(prev => ({ ...prev, loading: false }));
    }
  };

  // Funzione per confermare la rimozione del cavo/cavi dalla comanda
  const handleConfirmRemoveFromCommand = async () => {
    try {
      setRemoveFromCommandDialog(prev => ({ ...prev, loading: true }));

      const comandeService = await import('../../services/comandeService');

      if (removeFromCommandDialog.isMultiple && removeFromCommandDialog.selectedCavi) {
        // Modalità multipla: rimuovi tutti i cavi selezionati dalle loro comande
        let successCount = 0;
        let errorCount = 0;

        for (const cavo of removeFromCommandDialog.selectedCavi) {
          try {
            const comandaCorrente = cavo.comanda_posa || cavo.comanda_partenza ||
                                   cavo.comanda_arrivo || cavo.comanda_certificazione;

            if (comandaCorrente) {
              await comandeService.default.rimuoviCavoDaComanda(comandaCorrente, cavo.id_cavo);
              successCount++;
            }
          } catch (error) {
            console.error(`Errore nella rimozione del cavo ${cavo.id_cavo}:`, error);
            errorCount++;
          }
        }

        if (successCount > 0) {
          showNotification(
            `${successCount} cavi rimossi dalle comande${errorCount > 0 ? ` (${errorCount} errori)` : ''}`,
            errorCount > 0 ? 'warning' : 'success'
          );
        } else {
          showNotification('Errore nella rimozione dei cavi dalle comande', 'error');
        }

        // Deseleziona tutti i cavi
        setSelectedCaviAttivi([]);
        setSelectedCaviSpare([]);

      } else {
        // Modalità singola: rimuovi solo il cavo corrente
        await comandeService.default.rimuoviCavoDaComanda(
          removeFromCommandDialog.comandaCorrente,
          removeFromCommandDialog.cavo.id_cavo
        );

        showNotification(
          `Cavo ${removeFromCommandDialog.cavo.id_cavo} rimosso dalla comanda ${removeFromCommandDialog.comandaCorrente}`,
          'success'
        );
      }

      // Chiudi il dialog
      setRemoveFromCommandDialog({
        open: false,
        cavo: null,
        comandaCorrente: null,
        loading: false,
        isMultiple: false,
        selectedCavi: []
      });

      // Ricarica i dati
      setTimeout(() => fetchCavi(true), 500);

    } catch (error) {
      console.error('Errore nella rimozione del cavo dalla comanda:', error);
      showNotification('Errore nella rimozione del cavo dalla comanda', 'error');
      setRemoveFromCommandDialog(prev => ({ ...prev, loading: false }));
    }
  };

  // Funzione per aggiungere più cavi a una comanda esistente
  const handleAddMultipleToCommand = async () => {
    const allSelectedCavi = getAllSelectedCavi();

    if (allSelectedCavi.length === 0) {
      showNotification('Nessun cavo selezionato', 'warning');
      return;
    }

    console.log('🔄 Aggiunta multipla cavi a comanda:', allSelectedCavi.map(c => c.id_cavo));

    try {
      console.log('📋 Apertura dialog aggiunta multipla comanda...');
      setAddToCommandDialog({
        open: true,
        cavo: { id_cavo: `${allSelectedCavi.length} cavi selezionati` }, // Placeholder per il dialog
        comande: [],
        loading: true,
        isMultiple: true,
        selectedCavi: allSelectedCavi
      });

      // Carica le comande disponibili per il cantiere
      console.log('📡 Caricamento comande dal servizio...');
      const comandeService = await import('../../services/comandeService');
      const comande = await comandeService.default.getComande(cantiereId);
      console.log('📋 Comande ricevute:', comande);

      // Gestisci diversi formati di risposta
      let comandeArray = [];
      if (Array.isArray(comande)) {
        comandeArray = comande;
      } else if (comande && Array.isArray(comande.comande)) {
        comandeArray = comande.comande;
      } else if (comande && Array.isArray(comande.data)) {
        comandeArray = comande.data;
      }

      // Filtra le comande che possono accettare questi cavi
      const comandeDisponibili = comandeArray.filter(comanda => {
        return comanda.stato !== 'COMPLETATA' && comanda.stato !== 'CANCELLATA';
      });

      setAddToCommandDialog({
        open: true,
        cavo: { id_cavo: `${allSelectedCavi.length} cavi selezionati` },
        comande: comandeDisponibili,
        loading: false,
        isMultiple: true,
        selectedCavi: allSelectedCavi
      });

    } catch (error) {
      console.error('❌ Errore nel caricamento delle comande:', error);
      showNotification('Errore nel caricamento delle comande disponibili', 'error');
      setAddToCommandDialog({
        open: false,
        cavo: null,
        comande: [],
        loading: false,
        isMultiple: false,
        selectedCavi: []
      });
    }
  };

  // Funzione per rimuovere più cavi dalle loro comande
  const handleRemoveMultipleFromCommands = () => {
    const allSelectedCavi = getAllSelectedCavi();

    if (allSelectedCavi.length === 0) {
      showNotification('Nessun cavo selezionato', 'warning');
      return;
    }

    // Filtra solo i cavi che sono effettivamente assegnati a comande
    const caviConComande = allSelectedCavi.filter(cavo =>
      cavo.comanda_posa || cavo.comanda_partenza || cavo.comanda_arrivo || cavo.comanda_certificazione
    );

    if (caviConComande.length === 0) {
      showNotification('Nessuno dei cavi selezionati è assegnato a una comanda', 'info');
      return;
    }

    console.log('🔄 Rimozione multipla cavi da comande:', caviConComande.map(c => c.id_cavo));

    setRemoveFromCommandDialog({
      open: true,
      cavo: { id_cavo: `${caviConComande.length} cavi selezionati` },
      comandaCorrente: 'multiple',
      loading: false,
      isMultiple: true,
      selectedCavi: caviConComande
    });
  };

  // Definizione degli elementi del menu contestuale
  const getContextMenuItems = (cavo) => {
    const isSelected = caviAttivi.some(c => c.id_cavo === cavo?.id_cavo)
      ? selectedCaviAttivi.includes(cavo?.id_cavo)
      : selectedCaviSpare.includes(cavo?.id_cavo);

    const totalSelectedCount = getTotalSelectedCount();
    const hasMultipleSelection = totalSelectedCount > 1;

    // Menu base per singolo cavo
    const baseMenuItems = [
      {
        type: 'header',
        label: hasMultipleSelection ? `${totalSelectedCount} cavi selezionati` : `Cavo ${cavo?.id_cavo || ''}`
      },
      // Sezione comande multiple (solo se ci sono più cavi selezionati)
      ...(hasMultipleSelection ? [
        {
          type: 'header',
          label: `📋 Crea Comande Multiple (${totalSelectedCount} cavi)`
        },
        {
          id: 'create_command_posa',
          label: 'Comanda Posa',
          icon: <BuildIcon fontSize="small" />,
          action: 'create_command_posa',
          onClick: handleContextMenuAction,
          color: (() => {
            const validation = getQuickValidationStatus('POSA');
            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';
          })(),
          description: (() => {
            const validation = getQuickValidationStatus('POSA');
            let desc = `Crea comanda posa per ${totalSelectedCount} cavi`;
            if (validation.issues > 0) {
              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;
            }
            return desc;
          })()
        },
        {
          id: 'create_command_collegamento_partenza',
          label: 'Comanda Collegamento Partenza',
          icon: <LinkIcon fontSize="small" />,
          action: 'create_command_collegamento_partenza',
          onClick: handleContextMenuAction,
          color: (() => {
            const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');
            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';
          })(),
          description: (() => {
            const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');
            let desc = `Crea comanda collegamento partenza per ${totalSelectedCount} cavi`;
            if (validation.issues > 0) {
              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;
            }
            return desc;
          })()
        },
        {
          id: 'create_command_collegamento_arrivo',
          label: 'Comanda Collegamento Arrivo',
          icon: <LinkIcon fontSize="small" />,
          action: 'create_command_collegamento_arrivo',
          onClick: handleContextMenuAction,
          color: (() => {
            const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');
            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';
          })(),
          description: (() => {
            const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');
            let desc = `Crea comanda collegamento arrivo per ${totalSelectedCount} cavi`;
            if (validation.issues > 0) {
              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;
            }
            return desc;
          })()
        },
        {
          id: 'create_command_certificazione',
          label: 'Comanda Certificazione',
          icon: <VerifiedIcon fontSize="small" />,
          action: 'create_command_certificazione',
          onClick: handleContextMenuAction,
          color: (() => {
            const validation = getQuickValidationStatus('CERTIFICAZIONE');
            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';
          })(),
          description: (() => {
            const validation = getQuickValidationStatus('CERTIFICAZIONE');
            let desc = `Crea comanda certificazione per ${totalSelectedCount} cavi`;
            if (validation.issues > 0) {
              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;
            }
            return desc;
          })()
        },
        {
          type: 'divider'
        },
        // Sezione gestione comande esistenti per selezione multipla
        {
          type: 'header',
          label: `📋 Gestione Comande Esistenti (${totalSelectedCount} cavi)`
        },
        {
          id: 'add_multiple_to_command',
          label: 'Aggiungi Tutti a Comanda',
          icon: <AssignmentIcon fontSize="small" />,
          action: 'add_multiple_to_command',
          onClick: handleContextMenuAction,
          color: 'primary',
          description: `Aggiungi tutti i ${totalSelectedCount} cavi selezionati a una comanda esistente`
        },
        {
          id: 'remove_multiple_from_commands',
          label: 'Rimuovi Tutti dalle Comande',
          icon: <RemoveIcon fontSize="small" />,
          action: 'remove_multiple_from_commands',
          onClick: handleContextMenuAction,
          color: 'warning',
          description: `Rimuovi tutti i ${totalSelectedCount} cavi selezionati dalle loro comande attuali`
        },
        {
          type: 'divider'
        }
      ] : []),
      // Azioni singolo cavo (solo se non c'è selezione multipla)
      ...(!hasMultipleSelection ? [
        {
          id: 'view_details',
          label: 'Visualizza Dettagli',
          icon: <VisibilityIcon fontSize="small" />,
          action: 'view_details',
          onClick: handleContextMenuAction
        },
        {
          type: 'divider'
        },
        // Sezione gestione comande per singolo cavo
        {
          type: 'header',
          label: '📋 Gestione Comande'
        },
        {
          id: 'add_to_command',
          label: 'Aggiungi a Comanda',
          icon: <AssignmentIcon fontSize="small" />,
          action: 'add_to_command',
          onClick: handleContextMenuAction,
          color: 'primary',
          description: 'Aggiungi questo cavo a una comanda esistente'
        },
        ...(cavo?.comanda_posa || cavo?.comanda_partenza || cavo?.comanda_arrivo || cavo?.comanda_certificazione ? [
          {
            id: 'remove_from_command',
            label: 'Rimuovi da Comanda',
            icon: <RemoveIcon fontSize="small" />,
            action: 'remove_from_command',
            onClick: handleContextMenuAction,
            color: 'warning',
            description: `Rimuovi da comanda ${cavo?.comanda_posa || cavo?.comanda_partenza || cavo?.comanda_arrivo || cavo?.comanda_certificazione}`
          }
        ] : []),
        {
          type: 'divider'
        },
        {
          id: 'edit',
          label: 'Modifica',
          icon: <EditIcon fontSize="small" />,
          action: 'edit',
          onClick: handleContextMenuAction,
          color: 'primary'
        },
        {
          id: 'delete',
          label: 'Elimina',
          icon: <DeleteIcon fontSize="small" />,
          action: 'delete',
          onClick: handleContextMenuAction,
          color: 'error'
        },
        {
          type: 'divider'
        },
        {
          id: 'add_new',
          label: 'Aggiungi nuovo cavo',
          icon: <AddIcon fontSize="small" />,
          action: 'add_new',
          onClick: handleContextMenuAction,
          color: 'success'
        },
        {
          type: 'divider'
        }
      ] : []),
      // Azioni di selezione (sempre presenti)
      {
        id: 'select',
        label: isSelected ? 'Deseleziona' : 'Seleziona',
        icon: <SelectAllIcon fontSize="small" />,
        action: 'select',
        onClick: handleContextMenuAction,
        color: isSelected ? 'warning' : 'success'
      },
      // Azioni di copia (sempre presenti)
      {
        type: 'divider'
      },
      {
        id: 'copy_id',
        label: hasMultipleSelection ? 'Copia IDs Selezionati' : 'Copia ID',
        icon: <CopyIcon fontSize="small" />,
        action: 'copy_id',
        onClick: handleContextMenuAction,
        shortcut: 'Ctrl+C'
      },
      {
        id: 'copy_details',
        label: hasMultipleSelection ? 'Copia Dettagli Selezionati' : 'Copia Dettagli',
        icon: <CopyIcon fontSize="small" />,
        action: 'copy_details',
        onClick: handleContextMenuAction,
        description: hasMultipleSelection ? 'Copia dettagli di tutti i cavi selezionati' : 'Copia ID, tipologia, sezione e metri'
      }
    ];

    return baseMenuItems;
  };

  // Funzioni per gestire le azioni sui pulsanti stato
  const handleStatusAction = async (cavo, actionType, actionLabel, comandaCode) => {
    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');
    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);
    console.log('Action label:', actionLabel);
    console.log('Codice comanda:', comandaCode);

    if (actionType === 'insert_meters') {
      // Apri il dialogo per inserire i metri posati
      setInserisciMetriDialog({
        open: true,
        cavo: cavo,
        loading: false
      });
    } else if (actionType === 'modify_reel') {
      // Apri il dialog completo per modificare la bobina con cavo preselezionato
      console.log('Apertura dialog modifica bobina per cavo:', cavo.id_cavo);
      setModificaBobinaDialog({
        open: true,
        cavo: cavo,
        loading: false
      });
    } else if (actionType === 'connect_cable' || actionType === 'connect_arrival' ||
               actionType === 'connect_departure' || actionType === 'disconnect_cable' ||
               actionType === 'manage_connections') {

      // Verifica se il cavo è installato
      if (cavo.stato_installazione !== 'Installato') {
        console.log('Azione collegamenti ignorata per cavo non installato:', cavo.id_cavo);
        showNotification('I collegamenti sono disponibili solo per cavi installati', 'info');
        return;
      }

      // Cavo installato - apri il popup per gestire i collegamenti
      console.log('Apertura popup collegamenti per cavo installato:', cavo.id_cavo, 'azione:', actionType);

      // Usa setTimeout per evitare conflitti di stato
      setTimeout(() => {
        setCollegamentiDialog({
          open: true,
          cavo: cavo,
          loading: false
        });
      }, 50);
    } else if (actionType === 'create_certificate') {
      // Apri il dialog per creare una certificazione usando il componente esistente
      console.log('Apertura dialog creazione certificazione per cavo:', cavo.id_cavo);

      // Usa il componente CertificazioneCavi esistente con tutti i prerequisiti
      if (certificazioneRef.current) {
        certificazioneRef.current.createCertificationForCavo(cavo);
      }
    } else if (actionType === 'view_certificate') {
      // Apri il dialog per visualizzare la certificazione esistente
      console.log('Apertura dialog visualizzazione certificazione per cavo:', cavo.id_cavo);

      // Usa il componente CertificazioneCavi esistente per visualizzare
      if (certificazioneRef.current) {
        certificazioneRef.current.viewCertificationForCavo(cavo);
      }
    } else if (actionType === 'generate_pdf') {
      // Genera il PDF del certificato per il cavo
      console.log('Generazione PDF certificato per cavo:', cavo.id_cavo);

      // Usa il componente CertificazioneCavi esistente per generare il PDF
      if (certificazioneRef.current) {
        certificazioneRef.current.generatePdfForCavo(cavo);
      }
    } else if (actionType === 'view_command') {
      // Naviga alla pagina della comanda specifica
      console.log('🎯 Navigazione alla comanda:', comandaCode);
      console.log('🏗️ Cantiere ID:', cantiereId);

      if (comandaCode && cantiereId) {
        // Usa la pagina originale delle comande
        const comandeUrl = `/dashboard/cavi/comande?comanda=${comandaCode}`;
        console.log('🔗 URL di navigazione:', comandeUrl);

        // Naviga alla pagina comande originale
        window.location.href = comandeUrl;

        showNotification(`Apertura comanda ${comandaCode}...`, 'info');
      } else {
        console.error('❌ Impossibile navigare: comandaCode o cantiereId mancanti', {
          comandaCode,
          cantiereId
        });
        showNotification('Errore: impossibile aprire la comanda', 'error');
      }
    }
  };




  // Funzioni per gestire i callback del dialog modifica bobina
  const handleModificaBobinaSuccess = (message) => {
    showNotification(message, 'success');
    // Ricarica i dati per aggiornare lo stato
    setTimeout(() => fetchCavi(true), 500);
  };

  const handleModificaBobinaError = (message) => {
    showNotification(message, 'error');
  };

  // Funzioni per chiudere i dialoghi
  const handleCloseInserisciMetri = () => {
    if (!inserisciMetriDialog.loading) {
      setInserisciMetriDialog({ open: false, cavo: null, loading: false });
    }
  };

  const handleCloseModificaBobina = () => {
    if (!modificaBobinaDialog.loading) {
      setModificaBobinaDialog({ open: false, cavo: null, loading: false });
    }
  };

  const handleCloseCollegamenti = useCallback(() => {
    if (!collegamentiDialog.loading) {
      setCollegamentiDialog(prev => ({ ...prev, open: false }));
    }
  }, [collegamentiDialog.loading]);





  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale

  // Dashboard minimal con statistiche essenziali per visualizzazione cavi
  const renderDashboard = () => (
    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
      <Stack direction="row" spacing={4} alignItems="center" justifyContent="space-between" flexWrap="wrap">
        {/* Statistiche essenziali in formato compatto */}
        <Stack direction="row" alignItems="center" spacing={1}>
          <CableIcon color="primary" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.totaleCavi}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Totale
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <CheckCircleIcon color="success" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.caviInstallati}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Installati
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <LinkIcon color="info" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.caviCollegati}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Collegati
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <VerifiedIcon color="warning" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.caviCertificati}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Certificati
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <Box sx={{
            width: 32,
            height: 32,
            borderRadius: '50%',
            bgcolor: statistics.iap >= 80 ? 'success.main' :
                     statistics.iap >= 50 ? 'warning.main' : 'error.main',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography variant="caption" fontWeight="bold" color="white">
              {statistics.iap}%
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" fontWeight="medium" sx={{ lineHeight: 1 }}>
              IAP (Avanzamento Ponderato)
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {statistics.metriInstallati}m installati
            </Typography>
          </Box>
        </Stack>

        {/* Revisione corrente */}
        {revisioneCorrente && (
          <Stack direction="row" alignItems="center" spacing={1}>
            <Box>
              <Typography variant="body2" fontWeight="bold" sx={{ lineHeight: 1 }}>
                {revisioneCorrente}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Revisione
              </Typography>
            </Box>
          </Stack>
        )}
      </Stack>
    </Paper>
  );

  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable

  // Rimossa funzione handleViewModeChange

  // Renderizza il dialogo dei dettagli del cavo
  const renderDetailsDialog = () => {
    if (!selectedCavo) return null;

    return (
      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth="md" fullWidth>
        <DialogTitle>
          Dettagli Cavo: {selectedCavo.id_cavo}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Informazioni Generali</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>
                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}
                <Typography variant="body2"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>
                {/* sh field is now a spare field (kept in DB but hidden in UI) */}
              </Box>

              <Typography variant="subtitle1" gutterBottom>Partenza</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Arrivo</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>
              </Box>

              <Typography variant="subtitle1" gutterBottom>Installazione</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>
                <Typography variant="body2"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>
                <Typography variant="body2"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>
                <Typography variant="body2"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Chiudi</Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable



  return (
    <Box className="cavi-page">
      {loading ? (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>
          <CircularProgress size={40} />
          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => window.location.reload()}
            sx={{ mt: 2 }}
          >
            Ricarica la pagina
          </Button>
        </Box>
      ) : error ? (
        <Box>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
            {error.includes('Network Error') && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.
                <br />
                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.
              </Typography>
            )}
          </Alert>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              className="primary-button"
              onClick={() => window.location.reload()}
            >
              Ricarica la pagina
            </Button>
          </Box>
        </Box>
      ) : (
        <Box>


          {/* Dashboard con statistiche avanzate */}
          {renderDashboard()}

          {/* Sezione Cavi */}
          <Box sx={{ mt: 4 }}>
            {/* Banner informativo per modalità selezione */}
            {selectionEnabled && (
              <Alert
                severity="info"
                sx={{ mb: 2 }}
                action={
                  <Button
                    color="inherit"
                    size="small"
                    onClick={() => setSelectionEnabled(false)}
                  >
                    Disabilita
                  </Button>
                }
              >
                <Typography variant="body2">
                  <strong>Modalità Selezione Attiva</strong> - Clicca sui cavi per selezionarli,
                  poi usa il <strong>tasto destro</strong> per creare comande multiple.
                  {getTotalSelectedCount() > 0 && (
                    <span> <strong>{getTotalSelectedCount()} cavi selezionati</strong></span>
                  )}
                </Typography>
              </Alert>
            )}

            {/* Contatore selezione compatto - solo quando ci sono cavi selezionati */}
            {selectionEnabled && getTotalSelectedCount() > 0 && (
              <Box sx={{ mb: 2 }}>
                <Chip
                  label={`${getTotalSelectedCount()} cavi selezionati`}
                  color="primary"
                  variant="filled"
                  size="small"
                />
              </Box>
            )}

            {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}
            {process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && (
              <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>
                {Object.keys(caviAttivi[0]).map(key => (
                  <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>
                ))}
              </Box>
            )}

            <CaviFilterableTable
              cavi={caviAttivi}
              loading={loading}
              onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}

              selectionEnabled={selectionEnabled}
              selectedCavi={selectedCaviAttivi}
              onSelectionChange={handleCaviAttiviSelectionChange}
              onSelectionToggle={handleSelectionToggle}
              contextMenuItems={getContextMenuItems}
              onContextMenuAction={handleContextMenuAction}
              onStatusAction={handleStatusAction}
            />
            {caviAttivi.length === 0 && !loading && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Nessun cavo attivo trovato. I cavi attivi appariranno qui.
              </Alert>
            )}
          </Box>

          {/* Sezione Cavi Spare */}
          <Box sx={{ mt: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5">
                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}
              </Typography>
            </Box>
            <CaviFilterableTable
              cavi={caviSpare}
              loading={loading}
              onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}
              selectionEnabled={selectionEnabled}
              selectedCavi={selectedCaviSpare}
              onSelectionChange={handleCaviSpareSelectionChange}
              onSelectionToggle={handleSelectionToggle}
              contextMenuItems={getContextMenuItems}
              onContextMenuAction={handleContextMenuAction}
              onStatusAction={handleStatusAction}
            />
            {caviSpare.length === 0 && !loading && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.
              </Alert>
            )}
          </Box>

          {/* Rimossa sezione Debug */}

          {/* Dialogo dei dettagli del cavo */}
          {renderDetailsDialog()}

          {/* OBSOLETO: Dialog eliminazione cavo rimosso - Funzionalità integrata nel menu contestuale */}

          {/* OBSOLETO: Dialog modifica cavo rimosso - Funzionalità integrata nel menu contestuale */}

          {/* OBSOLETO: Dialog aggiunta cavo rimosso - Funzionalità integrata nel menu contestuale */}

          {/* Dialoghi per azioni sui pulsanti stato */}
          <InserisciMetriDialogCompleto
            open={inserisciMetriDialog.open}
            onClose={handleCloseInserisciMetri}
            cavo={inserisciMetriDialog.cavo}
            cantiereId={cantiereId}
            onSuccess={(message) => {
              showNotification(message, 'success');
              // Ricarica i dati per aggiornare lo stato
              setTimeout(() => fetchCavi(true), 500);
            }}
            onError={(message) => {
              showNotification(message, 'error');
            }}
            loading={inserisciMetriDialog.loading}
          />

          <ModificaBobinaDialogCompleto
            open={modificaBobinaDialog.open}
            onClose={handleCloseModificaBobina}
            cavo={modificaBobinaDialog.cavo}
            cantiereId={cantiereId}
            onSuccess={handleModificaBobinaSuccess}
            onError={handleModificaBobinaError}
          />

          {/* Dialog per la gestione collegamenti */}
          <Dialog
            open={collegamentiDialog.open}
            onClose={handleCloseCollegamenti}
            maxWidth="md"
            fullWidth
            disableEscapeKeyDown={false}
            keepMounted={false}
            disablePortal={false}
            disableScrollLock={true}
            hideBackdrop={false}
            disableAutoFocus={true}
            disableEnforceFocus={true}
            disableRestoreFocus={true}
            transitionDuration={0}
            TransitionProps={{
              timeout: 0,
              appear: false,
              enter: false,
              exit: false
            }}
            PaperProps={{
              style: {
                transition: 'none',
                transform: 'none'
              }
            }}
          >
            <DialogContent>
              {collegamentiDialog.cavo && (
                <CollegamentiCavo
                  cantiereId={cantiereId}
                  selectedCavo={collegamentiDialog.cavo}
                  onSuccess={(message) => {
                    if (message) {
                      showNotification(message, 'success');
                      // Chiudi il dialog immediatamente
                      setCollegamentiDialog(prev => ({ ...prev, open: false }));
                      // Ricarica i dati per aggiornare lo stato dei collegamenti
                      setTimeout(() => fetchCavi(true), 300);
                    }
                    // Non chiudere il dialog se message è null (annullamento)
                  }}
                  onError={(message) => {
                    showNotification(message, 'error');
                  }}
                  onClose={handleCloseCollegamenti}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Dialog per la creazione di comande */}
          <CreaComandaConCavi
            open={createCommandDialog.open}
            onClose={handleCloseCreateCommand}
            onSuccess={handleCreateCommandSuccess}
            cantiereId={cantiereId}
            tipoComandaPreselezionato={createCommandDialog.tipoComanda}
            caviPreselezionati={createCommandDialog.caviSelezionati}
          />

          {/* Componente CertificazioneCaviImproved per i dialog completi */}
          <Box sx={{ display: 'none' }}>
            <CertificazioneCaviImproved
              ref={certificazioneRef}
              cantiereId={cantiereId}
              onSuccess={(message) => {
                showNotification(message, 'success');
                // Ricarica i dati per aggiornare lo stato di certificazione
                setTimeout(() => fetchCavi(true), 500);
              }}
              onError={(message) => {
                showNotification(message, 'error');
              }}
            />
          </Box>

          {/* Dialog per aggiungere cavo a comanda */}
          <Dialog
            open={addToCommandDialog.open}
            onClose={() => setAddToCommandDialog({
              open: false,
              cavo: null,
              comande: [],
              loading: false,
              isMultiple: false,
              selectedCavi: []
            })}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {addToCommandDialog.isMultiple ? 'Aggiungi Cavi a Comanda' : 'Aggiungi Cavo a Comanda'}
            </DialogTitle>
            <DialogContent>
              {addToCommandDialog.cavo && (
                <Box sx={{ pt: 2 }}>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    <strong>{addToCommandDialog.isMultiple ? 'Cavi:' : 'Cavo:'}</strong> {addToCommandDialog.cavo.id_cavo}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Seleziona la comanda a cui aggiungere {addToCommandDialog.isMultiple ? 'questi cavi' : 'questo cavo'}:
                  </Typography>

                  {addToCommandDialog.loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                      <CircularProgress />
                    </Box>
                  ) : addToCommandDialog.comande.length === 0 ? (
                    <Alert severity="info">
                      Nessuna comanda disponibile per questo cavo.
                    </Alert>
                  ) : (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {addToCommandDialog.comande.map((comanda) => (
                        <Button
                          key={comanda.codice_comanda}
                          variant="outlined"
                          onClick={() => handleConfirmAddToCommand(comanda)}
                          sx={{
                            justifyContent: 'flex-start',
                            textAlign: 'left',
                            p: 2
                          }}
                        >
                          <Box>
                            <Typography variant="body1" fontWeight="bold">
                              {comanda.codice_comanda}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {comanda.tipo_comanda} • {comanda.responsabile} • {comanda.stato}
                            </Typography>
                          </Box>
                        </Button>
                      ))}
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setAddToCommandDialog({
                  open: false,
                  cavo: null,
                  comande: [],
                  loading: false,
                  isMultiple: false,
                  selectedCavi: []
                })}
                disabled={addToCommandDialog.loading}
              >
                Annulla
              </Button>
            </DialogActions>
          </Dialog>

          {/* Dialog per rimuovere cavo da comanda */}
          <Dialog
            open={removeFromCommandDialog.open}
            onClose={() => setRemoveFromCommandDialog({
              open: false,
              cavo: null,
              comandaCorrente: null,
              loading: false,
              isMultiple: false,
              selectedCavi: []
            })}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {removeFromCommandDialog.isMultiple ? 'Rimuovi Cavi dalle Comande' : 'Rimuovi Cavo da Comanda'}
            </DialogTitle>
            <DialogContent>
              {removeFromCommandDialog.cavo && (
                <Box sx={{ pt: 2 }}>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    <strong>{removeFromCommandDialog.isMultiple ? 'Cavi:' : 'Cavo:'}</strong> {removeFromCommandDialog.cavo.id_cavo}
                  </Typography>
                  {!removeFromCommandDialog.isMultiple && (
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      <strong>Comanda:</strong> {removeFromCommandDialog.comandaCorrente}
                    </Typography>
                  )}
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    Sei sicuro di voler rimuovere {removeFromCommandDialog.isMultiple ? 'questi cavi dalle loro comande' : 'questo cavo dalla comanda'}?
                    Questa azione non può essere annullata.
                  </Alert>
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setRemoveFromCommandDialog({
                  open: false,
                  cavo: null,
                  comandaCorrente: null,
                  loading: false,
                  isMultiple: false,
                  selectedCavi: []
                })}
                disabled={removeFromCommandDialog.loading}
              >
                Annulla
              </Button>
              <Button
                onClick={handleConfirmRemoveFromCommand}
                color="warning"
                variant="contained"
                disabled={removeFromCommandDialog.loading}
              >
                {removeFromCommandDialog.loading ? <CircularProgress size={20} /> : 'Rimuovi'}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Snackbar per le notifiche */}
          <Snackbar
            open={notification.open}
            autoHideDuration={4000}
            onClose={handleCloseNotification}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
              {notification.message}
            </Alert>
          </Snackbar>
        </Box>
      )}
    </Box>
  );
};

export default VisualizzaCaviPage;
