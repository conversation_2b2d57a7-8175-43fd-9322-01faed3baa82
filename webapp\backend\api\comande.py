from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from ..core.security import get_current_active_user
from ..database import get_db
from ..models.user import User
from ..models.comanda import <PERSON><PERSON><PERSON>, ComandaDettaglio
from ..models.cavo import Cavo
from ..schemas.comanda import (
    ComandaBase, ComandaCreate, ComandaUpdate, ComandaInDB, ComandaResponse,
    ComandaDettagliataResponse, ComandaListResponse, StatisticheComande,
    AssegnaCaviRequest, DatiPosaRequest, DatiCollegamentoRequest, DatiCertificazioneRequest, CambiaStatoRequest
)

# Importa le funzioni dal modulo comande esistente
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from modules.comande_new import (
    crea_comanda, assegna_cavi_a_comanda, ottieni_cavi_comanda,
    aggiorna_dati_posa, aggiorna_dati_collegamento, aggiorna_dati_certificazione,
    ottieni_comande_cantiere, ottieni_dettagli_comanda, elimina_comanda, cambia_stato_comanda,
    crea_comanda_con_cavi, ottieni_cavi_disponibili_per_tipo_comanda
)

router = APIRouter()


async def aggiorna_dati_posa_con_bobine(codice_comanda: str, dati_posa: Dict[str, Any], db: Session) -> bool:
    """
    Aggiorna i dati di posa per una comanda con gestione delle associazioni bobine.

    Args:
        codice_comanda: Codice della comanda
        dati_posa: Dizionario con i dati di posa per ogni cavo
        db: Sessione del database

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        from ..models.cavo import Cavo
        from ..models.parco_cavi import ParcoCavo

        # Importa le funzioni necessarie dal modulo cavi
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
        from modules.cavi import update_stato_bobina

        # Verifica che la comanda esista
        comanda = db.query(Comanda).filter(Comanda.codice_comanda == codice_comanda).first()
        if not comanda:
            raise ValueError(f"Comanda {codice_comanda} non trovata")

        # Processa ogni cavo
        for id_cavo, dati_cavo in dati_posa.items():
            # Recupera il cavo dal database
            cavo = db.query(Cavo).filter(
                Cavo.id_cavo == id_cavo,
                Cavo.id_cantiere == comanda.id_cantiere
            ).first()

            if not cavo:
                print(f"⚠️ Cavo {id_cavo} non trovato nel cantiere {comanda.id_cantiere}")
                continue

            # Estrai i dati
            metri_reali = float(dati_cavo.get('metratura_reale', 0))
            id_bobina_nuova = dati_cavo.get('id_bobina', '')
            force_over = dati_cavo.get('force_over', False)
            note = dati_cavo.get('note', '')
            data_posa = dati_cavo.get('data_posa')

            # Salva la vecchia bobina per restituire i metri
            vecchia_bobina_id = cavo.id_bobina

            # Gestione associazione bobina
            if id_bobina_nuova and id_bobina_nuova != 'BOBINA_VUOTA':
                # Verifica che la bobina esista
                bobina = db.query(ParcoCavo).filter(
                    ParcoCavo.id_bobina == id_bobina_nuova,
                    ParcoCavo.id_cantiere == comanda.id_cantiere
                ).first()

                if not bobina:
                    raise ValueError(f"Bobina {id_bobina_nuova} non trovata nel cantiere {comanda.id_cantiere}")

                # Verifica compatibilità
                if bobina.tipologia != cavo.tipologia or bobina.sezione != cavo.sezione:
                    raise ValueError(f"Bobina {id_bobina_nuova} non compatibile con cavo {id_cavo} (tipologia/sezione diverse)")

                # Verifica disponibilità metri
                if metri_reali > bobina.metri_residui and not force_over:
                    raise ValueError(f"Bobina {id_bobina_nuova} ha solo {bobina.metri_residui}m residui, richiesti {metri_reali}m")

                # Restituisci metri alla vecchia bobina se necessario
                if vecchia_bobina_id and vecchia_bobina_id != 'BOBINA_VUOTA' and vecchia_bobina_id != id_bobina_nuova:
                    vecchia_bobina = db.query(ParcoCavo).filter(
                        ParcoCavo.id_bobina == vecchia_bobina_id,
                        ParcoCavo.id_cantiere == comanda.id_cantiere
                    ).first()

                    if vecchia_bobina and cavo.metratura_reale:
                        vecchia_bobina.metri_residui += cavo.metratura_reale
                        # Aggiorna stato vecchia bobina
                        if vecchia_bobina.metri_residui < 0:
                            vecchia_bobina.stato_bobina = "Over"
                        elif vecchia_bobina.metri_residui == 0:
                            vecchia_bobina.stato_bobina = "Terminata"
                        elif vecchia_bobina.metri_residui < vecchia_bobina.metri_totali:
                            vecchia_bobina.stato_bobina = "In uso"
                        else:
                            vecchia_bobina.stato_bobina = "Disponibile"

                # Sottrai metri dalla nuova bobina
                bobina.metri_residui -= metri_reali

                # Aggiorna stato nuova bobina
                if bobina.metri_residui < 0:
                    bobina.stato_bobina = "Over"
                elif bobina.metri_residui == 0:
                    bobina.stato_bobina = "Terminata"
                elif bobina.metri_residui < bobina.metri_totali:
                    bobina.stato_bobina = "In uso"
                else:
                    bobina.stato_bobina = "Disponibile"

                # Aggiorna il cavo
                cavo.id_bobina = id_bobina_nuova

            elif id_bobina_nuova == 'BOBINA_VUOTA':
                # Restituisci metri alla vecchia bobina se necessario
                if vecchia_bobina_id and vecchia_bobina_id != 'BOBINA_VUOTA':
                    vecchia_bobina = db.query(ParcoCavo).filter(
                        ParcoCavo.id_bobina == vecchia_bobina_id,
                        ParcoCavo.id_cantiere == comanda.id_cantiere
                    ).first()

                    if vecchia_bobina and cavo.metratura_reale:
                        vecchia_bobina.metri_residui += cavo.metratura_reale
                        # Aggiorna stato vecchia bobina
                        if vecchia_bobina.metri_residui < 0:
                            vecchia_bobina.stato_bobina = "Over"
                        elif vecchia_bobina.metri_residui == 0:
                            vecchia_bobina.stato_bobina = "Terminata"
                        elif vecchia_bobina.metri_residui < vecchia_bobina.metri_totali:
                            vecchia_bobina.stato_bobina = "In uso"
                        else:
                            vecchia_bobina.stato_bobina = "Disponibile"

                # Imposta bobina vuota
                cavo.id_bobina = 'BOBINA_VUOTA'

            # Aggiorna i dati del cavo
            cavo.metratura_reale = metri_reali
            cavo.stato_installazione = 'Installato'

            if note:
                cavo.note = note

            if data_posa:
                from datetime import datetime
                if isinstance(data_posa, str):
                    cavo.data_posa = datetime.fromisoformat(data_posa.replace('Z', '+00:00'))
                else:
                    cavo.data_posa = data_posa

        # Commit delle modifiche
        db.commit()
        print(f"✅ Dati di posa con bobine aggiornati per comanda {codice_comanda}")
        return True

    except Exception as e:
        db.rollback()
        print(f"❌ Errore nell'aggiornamento dati posa con bobine: {str(e)}")
        raise e


@router.post("/", response_model=ComandaResponse, status_code=status.HTTP_201_CREATED)
async def crea_nuova_comanda(
    comanda: ComandaCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Crea una nuova comanda.
    """
    try:
        # Usa la funzione del modulo esistente
        codice_comanda = crea_comanda(
            id_cantiere=comanda.id_cantiere,
            tipo_comanda=comanda.tipo_comanda,
            descrizione=comanda.descrizione or "",
            responsabile=comanda.responsabile or current_user.username,
            data_scadenza=comanda.data_scadenza,
            responsabile_email=getattr(comanda, 'responsabile_email', None),
            responsabile_telefono=getattr(comanda, 'responsabile_telefono', None),
            numero_componenti_squadra=getattr(comanda, 'numero_componenti_squadra', 1)
        )
        
        if not codice_comanda:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nella creazione della comanda"
            )
        
        # Recupera la comanda creata dal database
        comanda_db = db.query(Comanda).filter(Comanda.codice_comanda == codice_comanda).first()
        if not comanda_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comanda creata ma non trovata nel database"
            )
        
        # Calcola statistiche aggiuntive
        numero_cavi_assegnati = 0
        cavi_completati = 0
        percentuale_completamento = 0.0
        
        return ComandaResponse(
            **comanda_db.__dict__,
            numero_cavi_assegnati=numero_cavi_assegnati,
            cavi_completati=cavi_completati,
            percentuale_completamento=percentuale_completamento
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore interno del server: {str(e)}"
        )


@router.get("/cantiere/{id_cantiere}", response_model=ComandaListResponse)
async def ottieni_comande_per_cantiere(
    id_cantiere: int,
    stato: Optional[str] = Query(None, description="Filtra per stato"),
    responsabile: Optional[str] = Query(None, description="Filtra per responsabile"),
    pagina: int = Query(1, ge=1, description="Numero di pagina"),
    per_pagina: int = Query(50, ge=1, le=100, description="Elementi per pagina"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene la lista delle comande per un cantiere.
    """
    try:
        # Usa la funzione del modulo esistente
        comande_data = ottieni_comande_cantiere(id_cantiere, stato)

        # Filtra per responsabile se specificato
        if responsabile:
            comande_data = [c for c in comande_data if c.get('responsabile') == responsabile]

        # Calcola paginazione
        totale = len(comande_data)
        start_idx = (pagina - 1) * per_pagina
        end_idx = start_idx + per_pagina
        comande_paginate = comande_data[start_idx:end_idx]
        
        # Converti in ComandaResponse con statistiche
        comande_response = []
        for comanda_data in comande_paginate:
            # Calcola statistiche per ogni comanda
            cavi_assegnati = ottieni_cavi_comanda(comanda_data['codice_comanda'])
            numero_cavi_assegnati = len(cavi_assegnati)
            
            cavi_completati = sum(1 for cavo in cavi_assegnati 
                                if cavo.get('stato_installazione') == 'Installato')
            
            percentuale_completamento = (
                (cavi_completati / numero_cavi_assegnati * 100) 
                if numero_cavi_assegnati > 0 else 0.0
            )
            
            comande_response.append(ComandaResponse(
                **comanda_data,
                numero_cavi_assegnati=numero_cavi_assegnati,
                cavi_completati=cavi_completati,
                percentuale_completamento=percentuale_completamento
            ))
        
        return ComandaListResponse(
            comande=comande_response,
            totale=totale,
            pagina=pagina,
            per_pagina=per_pagina
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero delle comande: {str(e)}"
        )


@router.get("/{codice_comanda}", response_model=ComandaDettagliataResponse)
async def ottieni_dettagli_comanda_completa(
    codice_comanda: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene i dettagli completi di una comanda inclusi i cavi assegnati.
    """
    try:
        # Usa le funzioni del modulo esistente
        dettagli_comanda = ottieni_dettagli_comanda(codice_comanda)
        if not dettagli_comanda:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comanda non trovata"
            )
        
        cavi_assegnati = ottieni_cavi_comanda(codice_comanda)
        
        # Calcola statistiche
        numero_cavi_assegnati = len(cavi_assegnati)
        cavi_completati = sum(1 for cavo in cavi_assegnati 
                            if cavo.get('stato_installazione') == 'Installato')
        percentuale_completamento = (
            (cavi_completati / numero_cavi_assegnati * 100) 
            if numero_cavi_assegnati > 0 else 0.0
        )
        
        # Ottieni dettagli dalla tabella ComandaDettaglio
        dettagli_db = db.query(ComandaDettaglio).filter(
            ComandaDettaglio.codice_comanda == codice_comanda
        ).all()
        
        return ComandaDettagliataResponse(
            **dettagli_comanda,
            numero_cavi_assegnati=numero_cavi_assegnati,
            cavi_completati=cavi_completati,
            percentuale_completamento=percentuale_completamento,
            cavi=cavi_assegnati,
            dettagli=[det.__dict__ for det in dettagli_db]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero dei dettagli: {str(e)}"
        )


@router.post("/{codice_comanda}/assegna-cavi", response_model=Dict[str, Any])
async def assegna_cavi(
    codice_comanda: str,
    request: AssegnaCaviRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Assegna una lista di cavi a una comanda.
    """
    try:
        # Usa la funzione del modulo esistente
        successo = assegna_cavi_a_comanda(codice_comanda, request.lista_id_cavi)
        
        if not successo:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'assegnazione dei cavi alla comanda"
            )
        
        return {
            "message": f"Cavi assegnati con successo alla comanda {codice_comanda}",
            "codice_comanda": codice_comanda,
            "cavi_assegnati": len(request.lista_id_cavi)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'assegnazione dei cavi: {str(e)}"
        )


@router.delete("/{codice_comanda}/cavi/{id_cavo}", response_model=Dict[str, Any])
async def rimuovi_cavo_da_comanda(
    codice_comanda: str,
    id_cavo: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Rimuove un cavo specifico da una comanda.
    """
    try:
        from modules.comande_new import rimuovi_cavo_da_comanda as rimuovi_cavo_func

        successo = rimuovi_cavo_func(codice_comanda, id_cavo)

        if not successo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cavo non trovato nella comanda o errore nella rimozione"
            )

        return {
            "message": f"Cavo {id_cavo} rimosso con successo dalla comanda {codice_comanda}",
            "codice_comanda": codice_comanda,
            "id_cavo": id_cavo
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella rimozione del cavo: {str(e)}"
        )


@router.put("/{codice_comanda}/dati-posa", response_model=Dict[str, Any])
async def aggiorna_posa(
    codice_comanda: str,
    request: DatiPosaRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Aggiorna i dati di posa per i cavi di una comanda.
    """
    try:
        # Usa la funzione del modulo esistente
        successo = aggiorna_dati_posa(codice_comanda, request.dati_posa)

        if not successo:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di posa"
            )

        return {
            "message": f"Dati di posa aggiornati con successo per la comanda {codice_comanda}",
            "codice_comanda": codice_comanda,
            "cavi_aggiornati": len(request.dati_posa)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento dei dati di posa: {str(e)}"
        )


@router.put("/{codice_comanda}/dati-collegamento", response_model=Dict[str, Any])
async def aggiorna_collegamento(
    codice_comanda: str,
    request: DatiCollegamentoRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Aggiorna i dati di collegamento per i cavi di una comanda.
    """
    try:
        # Usa la funzione del modulo esistente
        successo = aggiorna_dati_collegamento(codice_comanda, request.dati_collegamento)

        if not successo:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di collegamento"
            )

        return {
            "message": f"Dati di collegamento aggiornati con successo per la comanda {codice_comanda}",
            "codice_comanda": codice_comanda,
            "cavi_aggiornati": len(request.dati_collegamento)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento dei dati di collegamento: {str(e)}"
        )


@router.put("/{codice_comanda}/stato", response_model=Dict[str, Any])
async def cambia_stato(
    codice_comanda: str,
    request: CambiaStatoRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Cambia lo stato di una comanda.
    """
    try:
        # Verifica che il nuovo stato sia valido
        stati_validi = ['CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA', 'ANNULLATA']
        if request.nuovo_stato not in stati_validi:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Stato non valido. Stati validi: {', '.join(stati_validi)}"
            )

        # Usa la funzione del modulo esistente
        successo = cambia_stato_comanda(codice_comanda, request.nuovo_stato)

        if not successo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comanda non trovata o errore nel cambio di stato"
            )

        return {
            "message": f"Stato della comanda {codice_comanda} cambiato in {request.nuovo_stato}",
            "codice_comanda": codice_comanda,
            "nuovo_stato": request.nuovo_stato
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel cambio di stato: {str(e)}"
        )


@router.put("/{codice_comanda}", response_model=Dict[str, Any])
async def aggiorna_comanda(
    codice_comanda: str,
    request: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Aggiorna i dati di una comanda inclusi tipo, responsabile, descrizione, note e data scadenza.
    Gestisce automaticamente la riassegnazione dei cavi se il tipo di comanda cambia.
    """
    try:
        from modules.comande_new import aggiorna_comanda_dati

        # Campi modificabili
        campi_modificabili = {
            'tipo_comanda': request.get('tipo_comanda'),
            'descrizione': request.get('descrizione'),
            'responsabile': request.get('responsabile'),
            'note_capo_cantiere': request.get('note_capo_cantiere'),
            'data_scadenza': request.get('data_scadenza')
        }

        # Rimuovi campi None
        campi_modificabili = {k: v for k, v in campi_modificabili.items() if v is not None}

        if not campi_modificabili:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Nessun campo valido da modificare"
            )

        successo = aggiorna_comanda_dati(codice_comanda, campi_modificabili)

        if not successo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comanda non trovata o errore nell'aggiornamento"
            )

        return {
            "message": f"Comanda {codice_comanda} aggiornata con successo",
            "codice_comanda": codice_comanda,
            "campi_aggiornati": list(campi_modificabili.keys())
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento della comanda: {str(e)}"
        )


@router.delete("/{codice_comanda}", response_model=Dict[str, Any])
async def elimina_comanda_endpoint(
    codice_comanda: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Elimina una comanda e rimuove i riferimenti dai cavi.
    """
    try:
        # Usa la funzione del modulo esistente
        successo = elimina_comanda(codice_comanda)

        if not successo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comanda non trovata o errore nell'eliminazione"
            )

        return {
            "message": f"Comanda {codice_comanda} eliminata con successo",
            "codice_comanda": codice_comanda
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'eliminazione della comanda: {str(e)}"
        )


@router.get("/cantiere/{id_cantiere}/statistiche", response_model=StatisticheComande)
async def ottieni_statistiche_comande(
    id_cantiere: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene le statistiche delle comande per un cantiere.
    """
    try:
        # Importa il modello Responsabile per il conteggio
        from ..models.responsabile import Responsabile

        # Conta i responsabili attivi per il cantiere
        responsabili_attivi = db.query(Responsabile).filter(
            Responsabile.id_cantiere == id_cantiere,
            Responsabile.attivo == True
        ).count()

        # Usa la funzione del modulo esistente per ottenere tutte le comande
        comande_data = ottieni_comande_cantiere(id_cantiere)

        # Filtra solo le comande attive (esclude quelle annullate)
        comande_attive = [c for c in comande_data if c['stato'] != 'ANNULLATA']

        # Calcola statistiche solo per comande attive
        totale_comande = len(comande_attive)
        comande_create = sum(1 for c in comande_attive if c['stato'] == 'CREATA')
        comande_assegnate = sum(1 for c in comande_attive if c['stato'] == 'ASSEGNATA')
        comande_in_corso_raw = sum(1 for c in comande_attive if c['stato'] == 'IN_CORSO')
        comande_completate = sum(1 for c in comande_attive if c['stato'] == 'COMPLETATA')
        comande_annullate = sum(1 for c in comande_data if c['stato'] == 'ANNULLATA')  # Conta separatamente per info

        # Logica corretta per "In Corso": include ASSEGNATA + IN_CORSO
        comande_in_corso = comande_assegnate + comande_in_corso_raw


        # Calcola percentuale di completamento medio solo per comande attive
        percentuali_completamento = []
        for comanda_data in comande_attive:  # Usa solo comande attive
            cavi_assegnati = ottieni_cavi_comanda(comanda_data['codice_comanda'])
            numero_cavi_assegnati = len(cavi_assegnati)

            if numero_cavi_assegnati > 0:
                cavi_completati = sum(1 for cavo in cavi_assegnati
                                    if cavo.get('stato_installazione') == 'Installato')
                percentuale = (cavi_completati / numero_cavi_assegnati) * 100
                percentuali_completamento.append(percentuale)

        percentuale_completamento_medio = (
            sum(percentuali_completamento) / len(percentuali_completamento)
            if percentuali_completamento else 0.0
        )

        return StatisticheComande(
            responsabili_attivi=responsabili_attivi,
            totale_comande=totale_comande,
            comande_create=comande_create,
            comande_assegnate=comande_assegnate,
            comande_in_corso=comande_in_corso,
            comande_completate=comande_completate,
            comande_annullate=comande_annullate,
            percentuale_completamento_medio=percentuale_completamento_medio
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel calcolo delle statistiche: {str(e)}"
        )


@router.get("/{codice_comanda}/cavi")
async def ottieni_cavi_comanda_endpoint(
    codice_comanda: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Ottiene la lista dei cavi assegnati a una comanda.
    """
    try:
        cavi = ottieni_cavi_comanda(codice_comanda)
        return cavi

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero dei cavi: {str(e)}"
        )


@router.post("/{codice_comanda}/dati-posa")
async def aggiorna_dati_posa_endpoint(
    codice_comanda: str,
    dati_posa: DatiPosaRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Aggiorna i dati di posa per una comanda.
    """
    try:
        success = aggiorna_dati_posa(codice_comanda, dati_posa.dict())

        if success:
            return {"message": "Dati di posa aggiornati con successo"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di posa"
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento dei dati di posa: {str(e)}"
        )


@router.post("/{codice_comanda}/dati-posa-bobine")
async def aggiorna_dati_posa_con_bobine_endpoint(
    codice_comanda: str,
    dati_posa: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Aggiorna i dati di posa per una comanda con gestione delle associazioni bobine.
    """
    try:
        success = await aggiorna_dati_posa_con_bobine(codice_comanda, dati_posa, db)

        if success:
            return {"message": "Dati di posa e associazioni bobine aggiornati con successo"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di posa con bobine"
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento dei dati di posa con bobine: {str(e)}"
        )


@router.post("/{codice_comanda}/dati-collegamento")
async def aggiorna_dati_collegamento_endpoint(
    codice_comanda: str,
    dati_collegamento: DatiCollegamentoRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Aggiorna i dati di collegamento per una comanda.
    """
    try:
        success = aggiorna_dati_collegamento(codice_comanda, dati_collegamento.dict())

        if success:
            return {"message": "Dati di collegamento aggiornati con successo"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di collegamento"
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento dei dati di collegamento: {str(e)}"
        )


@router.post("/{codice_comanda}/dati-certificazione")
async def aggiorna_dati_certificazione_endpoint(
    codice_comanda: str,
    dati_certificazione: DatiCertificazioneRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Aggiorna i dati di certificazione per una comanda.
    """
    try:
        success = aggiorna_dati_certificazione(codice_comanda, dati_certificazione.dict())

        if success:
            return {"message": "Dati di certificazione aggiornati con successo"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di certificazione"
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento dei dati di certificazione: {str(e)}"
        )


@router.put("/{codice_comanda}/certificazione", response_model=Dict[str, Any])
async def aggiorna_certificazione(
    codice_comanda: str,
    request: DatiCertificazioneRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Aggiorna i dati di certificazione per i cavi di una comanda.
    """
    try:
        # Prepara i dati di certificazione
        dati_certificazione = {
            'esito_complessivo': request.esito_complessivo,
            'data_certificazione': request.data_certificazione.isoformat() if request.data_certificazione else None,
            'note_generali': request.note_generali,
            'responsabile_certificazione': request.responsabile_certificazione or current_user.username,
            'dati_cavi': request.dati_cavi or {},
            'forza_completamento': request.forza_completamento
        }

        # Usa la funzione del modulo esistente
        success = aggiorna_dati_certificazione(codice_comanda, dati_certificazione)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di certificazione"
            )

        return {
            "message": "Dati di certificazione aggiornati con successo",
            "codice_comanda": codice_comanda,
            "esito_complessivo": request.esito_complessivo
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento della certificazione: {str(e)}"
        )


@router.get("/cantiere/{id_cantiere}/cavi-disponibili", response_model=Dict[str, Any])
async def ottieni_cavi_disponibili(
    id_cantiere: int,
    tipo_comanda: str = Query(..., description="Tipo di comanda per filtrare i cavi disponibili"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene la lista dei cavi disponibili per un determinato tipo di comanda.
    """
    try:
        cavi = ottieni_cavi_disponibili_per_tipo_comanda(id_cantiere, tipo_comanda)

        return {
            "cavi_disponibili": cavi,
            "totale": len(cavi),
            "tipo_comanda": tipo_comanda
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero dei cavi disponibili: {str(e)}"
        )


@router.post("/cantiere/{id_cantiere}/crea-con-cavi", response_model=ComandaResponse, status_code=status.HTTP_201_CREATED)
async def crea_comanda_con_cavi_endpoint(
    id_cantiere: int,
    request: ComandaBase,  # Usa ComandaBase invece di ComandaCreate per non richiedere id_cantiere nel body
    lista_id_cavi: List[str] = Query(..., description="Lista degli ID dei cavi da assegnare"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Crea una nuova comanda e assegna immediatamente i cavi specificati.
    Flusso migliorato: selezione cavi -> creazione comanda -> assegnazione automatica.
    """
    try:
        # Usa la funzione del modulo esistente
        codice_comanda = crea_comanda_con_cavi(
            id_cantiere=id_cantiere,
            tipo_comanda=request.tipo_comanda,
            descrizione=request.descrizione,
            responsabile=request.responsabile,
            lista_id_cavi=lista_id_cavi,
            data_scadenza=request.data_scadenza,
            responsabile_email=getattr(request, 'responsabile_email', None),
            responsabile_telefono=getattr(request, 'responsabile_telefono', None),
            numero_componenti_squadra=getattr(request, 'numero_componenti_squadra', 1)
        )

        if not codice_comanda:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nella creazione della comanda con cavi"
            )

        # Ottieni i dettagli della comanda creata
        dettagli = ottieni_dettagli_comanda(codice_comanda)
        if not dettagli:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comanda creata ma non trovata"
            )

        # Crea la risposta nel formato ComandaResponse
        return ComandaResponse(
            **dettagli,
            numero_cavi_assegnati=len(lista_id_cavi),
            cavi_completati=0,
            percentuale_completamento=0.0
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella creazione della comanda con cavi: {str(e)}"
        )
