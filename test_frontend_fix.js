// Test rapido per verificare che il componente InserimentoMetriDialog si carichi senza errori

const fs = require('fs');
const path = require('path');

function testComponentSyntax() {
    console.log('🧪 Test Sintassi Componente InserimentoMetriDialog');
    
    const dialogPath = path.join(__dirname, 'webapp/frontend/src/components/comande/InserimentoMetriDialog.js');
    
    if (!fs.existsSync(dialogPath)) {
        console.log('❌ File non trovato:', dialogPath);
        return false;
    }
    
    const content = fs.readFileSync(dialogPath, 'utf8');
    
    // Test sintassi base
    const syntaxChecks = [
        {
            name: 'Import Autocomplete',
            test: content.includes('Autocomplete')
        },
        {
            name: 'renderInput prop',
            test: content.includes('renderInput={(params)')
        },
        {
            name: 'TextField in renderInput',
            test: content.includes('TextField') && content.includes('{...params}')
        },
        {
            name: 'handleBobinaChange function',
            test: content.includes('const handleBobinaChange')
        },
        {
            name: 'getBobineCompatibili function',
            test: content.includes('const getBobineCompatibili')
        },
        {
            name: 'aggiornaDatiPosaConBobine call',
            test: content.includes('aggiornaDatiPosaConBobine')
        }
    ];
    
    let passed = 0;
    syntaxChecks.forEach(check => {
        if (check.test) {
            console.log(`   ✅ ${check.name}`);
            passed++;
        } else {
            console.log(`   ❌ ${check.name}`);
        }
    });
    
    console.log(`\n📊 Risultato: ${passed}/${syntaxChecks.length} controlli passati`);
    
    if (passed === syntaxChecks.length) {
        console.log('✅ Componente sembra sintatticamente corretto');
        return true;
    } else {
        console.log('❌ Alcuni controlli falliti');
        return false;
    }
}

function testServiceSyntax() {
    console.log('\n🧪 Test Sintassi Services');
    
    const comandeServicePath = path.join(__dirname, 'webapp/frontend/src/services/comandeService.js');
    const caviServicePath = path.join(__dirname, 'webapp/frontend/src/services/caviService.js');
    
    let allGood = true;
    
    // Test comandeService
    if (fs.existsSync(comandeServicePath)) {
        const content = fs.readFileSync(comandeServicePath, 'utf8');
        if (content.includes('aggiornaDatiPosaConBobine')) {
            console.log('   ✅ comandeService aggiornato');
        } else {
            console.log('   ❌ comandeService mancante aggiornaDatiPosaConBobine');
            allGood = false;
        }
    } else {
        console.log('   ❌ comandeService non trovato');
        allGood = false;
    }
    
    // Test caviService
    if (fs.existsSync(caviServicePath)) {
        const content = fs.readFileSync(caviServicePath, 'utf8');
        if (content.includes('getBobineDisponibili')) {
            console.log('   ✅ caviService aggiornato');
        } else {
            console.log('   ❌ caviService mancante getBobineDisponibili');
            allGood = false;
        }
    } else {
        console.log('   ❌ caviService non trovato');
        allGood = false;
    }
    
    return allGood;
}

function main() {
    console.log('🚀 Test Fix Frontend - Autocomplete renderInput');
    console.log('=' * 50);
    
    const componentOk = testComponentSyntax();
    const servicesOk = testServiceSyntax();
    
    console.log('\n' + '=' * 50);
    
    if (componentOk && servicesOk) {
        console.log('✅ Tutti i test passati! Il fix dovrebbe risolvere l\'errore renderInput.');
        console.log('\n📝 Modifiche applicate:');
        console.log('   • Aggiunta prop renderInput all\'Autocomplete');
        console.log('   • Corretta gestione stato asincrono nelle funzioni di validazione');
        console.log('   • Corretti riferimenti modello ParcoCavo nel backend');
    } else {
        console.log('❌ Alcuni test falliti. Verificare le modifiche.');
    }
}

if (require.main === module) {
    main();
}
