#!/usr/bin/env node
/**
 * Test per verificare che l'endpoint parco-cavi funzioni dopo il fix del modello
 */

const http = require('http');

function testEndpoint(url, description) {
    return new Promise((resolve) => {
        console.log(`🧪 Test: ${description}`);
        console.log(`   URL: ${url}`);
        
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || 80,
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            timeout: 5000,
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Test-Script'
            }
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   Status: ${res.statusCode}`);
                
                if (res.statusCode === 401) {
                    console.log(`   ✅ ${description} - Endpoint funziona (401 = autenticazione richiesta)`);
                    console.log(`   📄 Risposta: ${data}`);
                    resolve({ success: true, status: res.statusCode });
                } else if (res.statusCode === 500) {
                    console.log(`   ❌ ${description} - Errore server (500)`);
                    console.log(`   📄 Risposta: ${data}`);
                    
                    // Verifica se è ancora l'errore della colonna mancante
                    if (data.includes('id_tipologia_cavo') || data.includes('column') || data.includes('colonna')) {
                        console.log(`   🔍 Sembra ancora l'errore della colonna mancante`);
                        resolve({ success: false, error: 'Column error still present' });
                    } else {
                        console.log(`   🔍 Errore diverso - potrebbe essere risolto`);
                        resolve({ success: true, status: res.statusCode });
                    }
                } else {
                    console.log(`   ✅ ${description} - Status ${res.statusCode}`);
                    resolve({ success: true, status: res.statusCode });
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ ${description} - Errore: ${err.message}`);
            resolve({ success: false, error: err.message });
        });
        
        req.on('timeout', () => {
            console.log(`   ❌ ${description} - Timeout`);
            req.destroy();
            resolve({ success: false, error: 'Timeout' });
        });
        
        req.end();
    });
}

async function main() {
    console.log('🚀 Test Fix Parco Cavi - Verifica Rimozione Colonna id_tipologia_cavo');
    console.log('=' * 70);
    
    const tests = [
        {
            url: 'http://localhost:8001/api/parco-cavi/1',
            description: 'Parco Cavi Cantiere 1 (senza auth)'
        },
        {
            url: 'http://localhost:8001/api/parco-cavi/1?disponibili_only=true',
            description: 'Parco Cavi Disponibili (senza auth)'
        }
    ];
    
    let fixedCount = 0;
    
    for (const test of tests) {
        const result = await testEndpoint(test.url, test.description);
        
        // Se riceve 401 (autenticazione richiesta) invece di 500 (errore server),
        // significa che l'endpoint funziona correttamente
        if (result.success && (result.status === 401 || result.status === 200)) {
            fixedCount++;
        }
        
        console.log(''); // Riga vuota tra i test
    }
    
    console.log('=' * 70);
    console.log(`📊 Risultati: ${fixedCount}/${tests.length} endpoint funzionanti`);
    
    if (fixedCount === tests.length) {
        console.log('✅ PROBLEMA RISOLTO! L\'endpoint parco-cavi funziona correttamente.');
        console.log('   Il modello ParcoCavo è stato corretto rimuovendo id_tipologia_cavo.');
        console.log('   Ora riceve 401 (autenticazione richiesta) invece di 500 (errore server).');
        console.log('\n🎯 Prossimi passi:');
        console.log('   1. Testa l\'interfaccia Parco Cavi nel browser');
        console.log('   2. Verifica che le bobine si carichino correttamente');
        console.log('   3. Testa l\'inserimento metri posati con selezione bobine');
    } else {
        console.log('❌ Alcuni endpoint hanno ancora problemi.');
        console.log('   Verifica i log del backend per errori aggiuntivi.');
    }
    
    console.log('\n💡 Note:');
    console.log('   • 401 = Autenticazione richiesta (NORMALE)');
    console.log('   • 500 = Errore server (PROBLEMA)');
    console.log('   • 200 = Successo (IDEALE con auth)');
}

if (require.main === module) {
    main().catch(console.error);
}
