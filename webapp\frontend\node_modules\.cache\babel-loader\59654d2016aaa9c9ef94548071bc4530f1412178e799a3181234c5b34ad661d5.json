{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions, Snackbar, FormControl, InputLabel, Select, MenuItem, Stack } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport { Cable as CableIcon, CheckCircle as CheckCircleIcon, Schedule as ScheduleIcon, Link as LinkIcon, LinkOff as LinkOffIcon, Timeline as TimelineIcon, CheckBox as CheckBoxIcon, CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon, Visibility as VisibilityIcon, Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon, SelectAll as SelectAllIcon, ContentCopy as CopyIcon, Settings as SettingsIcon, Verified as VerifiedIcon, Build as BuildIcon, Assignment as AssignmentIcon, Remove as RemoveIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\n// import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti'; // OBSOLETO: Componente eliminato\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialogCompleto from '../../components/cavi/InserisciMetriDialogCompleto';\nimport ModificaBobinaDialogCompleto from '../../components/cavi/ModificaBobinaDialogCompleto';\nimport CollegamentiCavo from '../../components/cavi/CollegamentiCavo';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport CreaComandaConCavi from '../../components/comande/CreaComandaConCavi';\n// import comandeValidationService from '../../services/comandeValidationService';\n\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per i dialoghi di azione sui pulsanti stato\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [collegamentiDialog, setCollegamentiDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  // Ref per il componente CertificazioneCavi\n  const certificazioneRef = useRef(null);\n\n  // Stati per il dialog di creazione comande multiple\n  const [createCommandDialog, setCreateCommandDialog] = useState({\n    open: false,\n    tipoComanda: '',\n    caviSelezionati: [],\n    loading: false\n  });\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    iap: 0,\n    // Indice di Avanzamento Ponderato\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n  // Stato per la revisione corrente (solo per visualizzazione nelle statistiche)\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare l'Indice di Avanzamento Ponderato (IAP)\n  const calculateIAP = (nTot, nInst, nColl, nCert) => {\n    // Pesi per le fasi del progetto\n    const Wp = 2.0; // Peso fase Posa\n    const Wc = 1.5; // Peso fase Collegamento\n    const Wz = 0.5; // Peso fase Certificazione\n\n    // Se non ci sono cavi, ritorna 0\n    if (nTot === 0) return 0;\n\n    // Calcolo del numeratore (Sforzo Completato)\n    const sforzoSoloInstallati = (nInst - nColl) * Wp;\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc);\n    const sforzoCertificati = nCert * (Wp + Wc + Wz);\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati;\n\n    // Calcolo del denominatore (Sforzo Massimo Previsto)\n    const denominatore = nTot * (Wp + Wc + Wz);\n\n    // Calcolo finale dell'IAP in percentuale\n    const iap = numeratore / denominatore * 100;\n    console.log('Calcolo IAP:', {\n      nTot,\n      nInst,\n      nColl,\n      nCert,\n      pesi: {\n        Wp,\n        Wc,\n        Wz\n      },\n      sforzoSoloInstallati,\n      sforzoSoloCollegati,\n      sforzoCertificati,\n      numeratore,\n      denominatore,\n      iap: Math.round(iap * 100) / 100\n    });\n    return Math.round(iap * 100) / 100; // Arrotonda a 2 decimali\n  };\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: (caviAttiviData === null || caviAttiviData === void 0 ? void 0 : caviAttiviData.length) || 0,\n      caviSpare: (caviSpareData === null || caviSpareData === void 0 ? void 0 : caviSpareData.length) || 0,\n      totale: tuttiCavi.length\n    });\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').length;\n    const caviDaInstallare = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Da installare' || cavo.stato_installazione === 'DA_INSTALLARE').length;\n    const caviInCorso = tuttiCavi.filter(cavo => cavo.stato_installazione === 'In corso' || cavo.stato_installazione === 'IN_CORSO').length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo => cavo.collegamenti === 3 && cavo.responsabile_partenza && cavo.responsabile_arrivo).length;\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola certificazioni basandosi sul campo 'certificato' dei cavi\n    const caviCertificati = tuttiCavi.filter(cavo => cavo.certificato === true || cavo.certificato === 'true').length;\n\n    // Calcola l'Indice di Avanzamento Ponderato (IAP)\n    const iap = calculateIAP(totaleCavi, caviInstallati, caviCollegati, caviCertificati);\n\n    // Calcola percentuali tradizionali per confronto\n    const percentualeInstallazione = iap; // Sostituito con IAP\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round(caviCollegati / totaleCavi * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCertificazione = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      percentualeCertificazione,\n      iap,\n      // Indice di Avanzamento Ponderato\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare solo la revisione corrente (per le statistiche)\n  const loadRevisioneCorrente = async cantiereIdToUse => {\n    try {\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      // Il servizio restituisce un oggetto con la proprietà revisione_corrente\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n    } catch (error) {\n      console.error('Errore nel caricamento della revisione corrente:', error);\n      // Non bloccare l'applicazione se la revisione non è disponibile\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica la revisione corrente per le statistiche\n        await loadRevisioneCorrente(cantiereIdNum);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n  const handleCaviAttiviSelectionChange = selectedIds => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n  const handleCaviSpareSelectionChange = selectedIds => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Funzionalità di modifica cavo non ancora implementata\n        showNotification('Funzionalità di modifica cavo in sviluppo', 'info');\n        break;\n      case 'delete':\n        // Funzionalità di eliminazione cavo non ancora implementata\n        showNotification('Funzionalità di eliminazione cavo in sviluppo', 'info');\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        const totalSelectedCount = getTotalSelectedCount();\n        if (totalSelectedCount > 1) {\n          // Copia tutti gli ID dei cavi selezionati\n          const allSelectedCavi = getAllSelectedCavi();\n          const allIds = allSelectedCavi.map(c => c.id_cavo).join(', ');\n          navigator.clipboard.writeText(allIds);\n          showNotification(`${totalSelectedCount} IDs cavi copiati negli appunti`, 'success');\n        } else {\n          navigator.clipboard.writeText(cavo.id_cavo);\n          showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        }\n        break;\n      case 'copy_details':\n        const totalSelected = getTotalSelectedCount();\n        if (totalSelected > 1) {\n          // Copia dettagli di tutti i cavi selezionati\n          const allSelectedCavi = getAllSelectedCavi();\n          const allDetails = allSelectedCavi.map(c => `ID: ${c.id_cavo}\\nTipologia: ${c.tipologia}\\nSezione: ${c.sezione}\\nMetri: ${c.metri_teorici}`).join('\\n\\n');\n          navigator.clipboard.writeText(allDetails);\n          showNotification(`Dettagli di ${totalSelected} cavi copiati negli appunti`, 'success');\n        } else {\n          const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n          navigator.clipboard.writeText(details);\n          showNotification('Dettagli cavo copiati negli appunti', 'success');\n        }\n        break;\n      case 'add_new':\n        // Funzionalità di aggiunta cavo non ancora implementata\n        showNotification('Funzionalità di aggiunta cavo in sviluppo', 'info');\n        break;\n      // Nuove azioni per la creazione di comande multiple\n      case 'create_command_posa':\n        handleCreateMultipleCommand('POSA');\n        break;\n      case 'create_command_collegamento_partenza':\n        handleCreateMultipleCommand('COLLEGAMENTO_PARTENZA');\n        break;\n      case 'create_command_collegamento_arrivo':\n        handleCreateMultipleCommand('COLLEGAMENTO_ARRIVO');\n        break;\n      case 'create_command_certificazione':\n        handleCreateMultipleCommand('CERTIFICAZIONE');\n        break;\n      // Nuove azioni per gestione comande singolo cavo\n      case 'add_to_command':\n        handleAddToCommand(cavo);\n        break;\n      case 'remove_from_command':\n        handleRemoveFromCommand(cavo);\n        break;\n      // Nuove azioni per gestione comande multiple\n      case 'add_multiple_to_command':\n        handleAddMultipleToCommand();\n        break;\n      case 'remove_multiple_from_commands':\n        handleRemoveMultipleFromCommands();\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Funzione per controllare rapidamente la validazione di un tipo di comanda\n  const getQuickValidationStatus = tipoComanda => {\n    // Temporaneamente disabilitato per debug - restituisce sempre valido\n    return {\n      valid: true,\n      issues: 0,\n      errors: 0,\n      warnings: 0\n    };\n  };\n\n  // Funzione per gestire la creazione di comande multiple\n  const handleCreateMultipleCommand = tipoComanda => {\n    const allSelectedCavi = getAllSelectedCavi();\n    if (allSelectedCavi.length === 0) {\n      showNotification('Nessun cavo selezionato per la creazione della comanda', 'warning');\n      return;\n    }\n    console.log(`Creazione comanda ${tipoComanda} per ${allSelectedCavi.length} cavi:`, allSelectedCavi.map(c => c.id_cavo));\n\n    // Apri il dialog di creazione comanda con i cavi preselezionati\n    setCreateCommandDialog({\n      open: true,\n      tipoComanda: tipoComanda,\n      caviSelezionati: allSelectedCavi,\n      loading: false\n    });\n  };\n\n  // Funzione per chiudere il dialog di creazione comande\n  const handleCloseCreateCommand = () => {\n    setCreateCommandDialog({\n      open: false,\n      tipoComanda: '',\n      caviSelezionati: [],\n      loading: false\n    });\n  };\n\n  // Funzione per gestire il successo della creazione comanda\n  const handleCreateCommandSuccess = response => {\n    showNotification(`Comanda ${response.codice_comanda} creata con successo!`, 'success');\n\n    // Deseleziona tutti i cavi\n    setSelectedCaviAttivi([]);\n    setSelectedCaviSpare([]);\n\n    // Chiudi il dialog\n    handleCloseCreateCommand();\n\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n\n  // Funzione per gestire gli errori nella creazione comanda\n  const handleCreateCommandError = error => {\n    console.error('Errore nella creazione della comanda:', error);\n    showNotification('Errore nella creazione della comanda', 'error');\n  };\n\n  // Stati per i dialog di gestione comande singolo cavo\n  const [addToCommandDialog, setAddToCommandDialog] = useState({\n    open: false,\n    cavo: null,\n    comande: [],\n    loading: false\n  });\n  const [removeFromCommandDialog, setRemoveFromCommandDialog] = useState({\n    open: false,\n    cavo: null,\n    comandaCorrente: null,\n    loading: false\n  });\n\n  // Funzione per aggiungere un cavo a una comanda esistente\n  const handleAddToCommand = async cavo => {\n    console.log('🔄 Aggiunta cavo a comanda:', cavo.id_cavo);\n    console.log('🏗️ Cantiere ID:', cantiereId);\n    try {\n      console.log('📋 Apertura dialog aggiunta comanda...');\n      setAddToCommandDialog({\n        open: true,\n        cavo: cavo,\n        comande: [],\n        loading: true\n      });\n\n      // Carica le comande disponibili per il cantiere\n      console.log('📡 Caricamento comande dal servizio...');\n      const comandeService = await import('../../services/comandeService');\n      const comande = await comandeService.default.getComande(cantiereId);\n      console.log('📋 Comande ricevute:', comande);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comande)) {\n        comandeArray = comande;\n      } else if (comande && Array.isArray(comande.comande)) {\n        comandeArray = comande.comande;\n      } else if (comande && Array.isArray(comande.data)) {\n        comandeArray = comande.data;\n      }\n      console.log('📋 Array comande processato:', comandeArray);\n\n      // Filtra le comande che possono accettare questo cavo\n      const comandeDisponibili = comandeArray.filter(comanda => {\n        // Escludi comande completate o cancellate\n        return comanda.stato !== 'COMPLETATA' && comanda.stato !== 'CANCELLATA';\n      });\n      console.log('📋 Comande disponibili filtrate:', comandeDisponibili);\n      setAddToCommandDialog({\n        open: true,\n        cavo: cavo,\n        comande: comandeDisponibili,\n        loading: false\n      });\n    } catch (error) {\n      console.error('❌ Errore nel caricamento delle comande:', error);\n      showNotification('Errore nel caricamento delle comande disponibili', 'error');\n      setAddToCommandDialog({\n        open: false,\n        cavo: null,\n        comande: [],\n        loading: false\n      });\n    }\n  };\n\n  // Funzione per rimuovere un cavo da una comanda\n  const handleRemoveFromCommand = cavo => {\n    console.log('🔄 Rimozione cavo da comanda:', cavo.id_cavo);\n\n    // Determina la comanda corrente del cavo\n    const comandaCorrente = cavo.comanda_posa || cavo.comanda_partenza || cavo.comanda_arrivo || cavo.comanda_certificazione;\n    if (!comandaCorrente) {\n      showNotification('Il cavo non è assegnato a nessuna comanda', 'warning');\n      return;\n    }\n    setRemoveFromCommandDialog({\n      open: true,\n      cavo: cavo,\n      comandaCorrente: comandaCorrente,\n      loading: false\n    });\n  };\n\n  // Funzione per confermare l'aggiunta del cavo/cavi alla comanda\n  const handleConfirmAddToCommand = async comandaSelezionata => {\n    try {\n      setAddToCommandDialog(prev => ({\n        ...prev,\n        loading: true\n      }));\n      const comandeService = await import('../../services/comandeService');\n      if (addToCommandDialog.isMultiple && addToCommandDialog.selectedCavi) {\n        // Modalità multipla: aggiungi tutti i cavi selezionati\n        const listaIdCavi = addToCommandDialog.selectedCavi.map(cavo => cavo.id_cavo);\n        await comandeService.default.assegnaCaviAComanda(comandaSelezionata.codice_comanda, listaIdCavi);\n        showNotification(`${listaIdCavi.length} cavi aggiunti alla comanda ${comandaSelezionata.codice_comanda}`, 'success');\n\n        // Deseleziona tutti i cavi\n        setSelectedCaviAttivi([]);\n        setSelectedCaviSpare([]);\n      } else {\n        // Modalità singola: aggiungi solo il cavo corrente\n        await comandeService.default.assegnaCaviAComanda(comandaSelezionata.codice_comanda, [addToCommandDialog.cavo.id_cavo]);\n        showNotification(`Cavo ${addToCommandDialog.cavo.id_cavo} aggiunto alla comanda ${comandaSelezionata.codice_comanda}`, 'success');\n      }\n\n      // Chiudi il dialog\n      setAddToCommandDialog({\n        open: false,\n        cavo: null,\n        comande: [],\n        loading: false,\n        isMultiple: false,\n        selectedCavi: []\n      });\n\n      // Ricarica i dati\n      setTimeout(() => fetchCavi(true), 500);\n    } catch (error) {\n      console.error('Errore nell\\'aggiunta del cavo alla comanda:', error);\n      showNotification('Errore nell\\'aggiunta del cavo alla comanda', 'error');\n      setAddToCommandDialog(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n\n  // Funzione per confermare la rimozione del cavo/cavi dalla comanda\n  const handleConfirmRemoveFromCommand = async () => {\n    try {\n      setRemoveFromCommandDialog(prev => ({\n        ...prev,\n        loading: true\n      }));\n      const comandeService = await import('../../services/comandeService');\n      if (removeFromCommandDialog.isMultiple && removeFromCommandDialog.selectedCavi) {\n        // Modalità multipla: rimuovi tutti i cavi selezionati dalle loro comande\n        let successCount = 0;\n        let errorCount = 0;\n        for (const cavo of removeFromCommandDialog.selectedCavi) {\n          try {\n            const comandaCorrente = cavo.comanda_posa || cavo.comanda_partenza || cavo.comanda_arrivo || cavo.comanda_certificazione;\n            if (comandaCorrente) {\n              await comandeService.default.rimuoviCavoDaComanda(comandaCorrente, cavo.id_cavo);\n              successCount++;\n            }\n          } catch (error) {\n            console.error(`Errore nella rimozione del cavo ${cavo.id_cavo}:`, error);\n            errorCount++;\n          }\n        }\n        if (successCount > 0) {\n          showNotification(`${successCount} cavi rimossi dalle comande${errorCount > 0 ? ` (${errorCount} errori)` : ''}`, errorCount > 0 ? 'warning' : 'success');\n        } else {\n          showNotification('Errore nella rimozione dei cavi dalle comande', 'error');\n        }\n\n        // Deseleziona tutti i cavi\n        setSelectedCaviAttivi([]);\n        setSelectedCaviSpare([]);\n      } else {\n        // Modalità singola: rimuovi solo il cavo corrente\n        await comandeService.default.rimuoviCavoDaComanda(removeFromCommandDialog.comandaCorrente, removeFromCommandDialog.cavo.id_cavo);\n        showNotification(`Cavo ${removeFromCommandDialog.cavo.id_cavo} rimosso dalla comanda ${removeFromCommandDialog.comandaCorrente}`, 'success');\n      }\n\n      // Chiudi il dialog\n      setRemoveFromCommandDialog({\n        open: false,\n        cavo: null,\n        comandaCorrente: null,\n        loading: false,\n        isMultiple: false,\n        selectedCavi: []\n      });\n\n      // Ricarica i dati\n      setTimeout(() => fetchCavi(true), 500);\n    } catch (error) {\n      console.error('Errore nella rimozione del cavo dalla comanda:', error);\n      showNotification('Errore nella rimozione del cavo dalla comanda', 'error');\n      setRemoveFromCommandDialog(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n\n  // Funzione per aggiungere più cavi a una comanda esistente\n  const handleAddMultipleToCommand = async () => {\n    const allSelectedCavi = getAllSelectedCavi();\n    if (allSelectedCavi.length === 0) {\n      showNotification('Nessun cavo selezionato', 'warning');\n      return;\n    }\n    console.log('🔄 Aggiunta multipla cavi a comanda:', allSelectedCavi.map(c => c.id_cavo));\n    try {\n      console.log('📋 Apertura dialog aggiunta multipla comanda...');\n      setAddToCommandDialog({\n        open: true,\n        cavo: {\n          id_cavo: `${allSelectedCavi.length} cavi selezionati`\n        },\n        // Placeholder per il dialog\n        comande: [],\n        loading: true,\n        isMultiple: true,\n        selectedCavi: allSelectedCavi\n      });\n\n      // Carica le comande disponibili per il cantiere\n      console.log('📡 Caricamento comande dal servizio...');\n      const comandeService = await import('../../services/comandeService');\n      const comande = await comandeService.default.getComande(cantiereId);\n      console.log('📋 Comande ricevute:', comande);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comande)) {\n        comandeArray = comande;\n      } else if (comande && Array.isArray(comande.comande)) {\n        comandeArray = comande.comande;\n      } else if (comande && Array.isArray(comande.data)) {\n        comandeArray = comande.data;\n      }\n\n      // Filtra le comande che possono accettare questi cavi\n      const comandeDisponibili = comandeArray.filter(comanda => {\n        return comanda.stato !== 'COMPLETATA' && comanda.stato !== 'CANCELLATA';\n      });\n      setAddToCommandDialog({\n        open: true,\n        cavo: {\n          id_cavo: `${allSelectedCavi.length} cavi selezionati`\n        },\n        comande: comandeDisponibili,\n        loading: false,\n        isMultiple: true,\n        selectedCavi: allSelectedCavi\n      });\n    } catch (error) {\n      console.error('❌ Errore nel caricamento delle comande:', error);\n      showNotification('Errore nel caricamento delle comande disponibili', 'error');\n      setAddToCommandDialog({\n        open: false,\n        cavo: null,\n        comande: [],\n        loading: false,\n        isMultiple: false,\n        selectedCavi: []\n      });\n    }\n  };\n\n  // Funzione per rimuovere più cavi dalle loro comande\n  const handleRemoveMultipleFromCommands = () => {\n    const allSelectedCavi = getAllSelectedCavi();\n    if (allSelectedCavi.length === 0) {\n      showNotification('Nessun cavo selezionato', 'warning');\n      return;\n    }\n\n    // Filtra solo i cavi che sono effettivamente assegnati a comande\n    const caviConComande = allSelectedCavi.filter(cavo => cavo.comanda_posa || cavo.comanda_partenza || cavo.comanda_arrivo || cavo.comanda_certificazione);\n    if (caviConComande.length === 0) {\n      showNotification('Nessuno dei cavi selezionati è assegnato a una comanda', 'info');\n      return;\n    }\n    console.log('🔄 Rimozione multipla cavi da comande:', caviConComande.map(c => c.id_cavo));\n    setRemoveFromCommandDialog({\n      open: true,\n      cavo: {\n        id_cavo: `${caviConComande.length} cavi selezionati`\n      },\n      comandaCorrente: 'multiple',\n      loading: false,\n      isMultiple: true,\n      selectedCavi: caviConComande\n    });\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = cavo => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === (cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo)) ? selectedCaviAttivi.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) : selectedCaviSpare.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo);\n    const totalSelectedCount = getTotalSelectedCount();\n    const hasMultipleSelection = totalSelectedCount > 1;\n\n    // Menu base per singolo cavo\n    const baseMenuItems = [{\n      type: 'header',\n      label: hasMultipleSelection ? `${totalSelectedCount} cavi selezionati` : `Cavo ${(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) || ''}`\n    },\n    // Sezione comande multiple (solo se ci sono più cavi selezionati)\n    ...(hasMultipleSelection ? [{\n      type: 'header',\n      label: `📋 Crea Comande Multiple (${totalSelectedCount} cavi)`\n    }, {\n      id: 'create_command_posa',\n      label: 'Comanda Posa',\n      icon: /*#__PURE__*/_jsxDEV(BuildIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1226,\n        columnNumber: 17\n      }, this),\n      action: 'create_command_posa',\n      onClick: handleContextMenuAction,\n      color: (() => {\n        const validation = getQuickValidationStatus('POSA');\n        return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n      })(),\n      description: (() => {\n        const validation = getQuickValidationStatus('POSA');\n        let desc = `Crea comanda posa per ${totalSelectedCount} cavi`;\n        if (validation.issues > 0) {\n          desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n        }\n        return desc;\n      })()\n    }, {\n      id: 'create_command_collegamento_partenza',\n      label: 'Comanda Collegamento Partenza',\n      icon: /*#__PURE__*/_jsxDEV(LinkIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1245,\n        columnNumber: 17\n      }, this),\n      action: 'create_command_collegamento_partenza',\n      onClick: handleContextMenuAction,\n      color: (() => {\n        const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');\n        return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n      })(),\n      description: (() => {\n        const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');\n        let desc = `Crea comanda collegamento partenza per ${totalSelectedCount} cavi`;\n        if (validation.issues > 0) {\n          desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n        }\n        return desc;\n      })()\n    }, {\n      id: 'create_command_collegamento_arrivo',\n      label: 'Comanda Collegamento Arrivo',\n      icon: /*#__PURE__*/_jsxDEV(LinkIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1264,\n        columnNumber: 17\n      }, this),\n      action: 'create_command_collegamento_arrivo',\n      onClick: handleContextMenuAction,\n      color: (() => {\n        const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');\n        return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n      })(),\n      description: (() => {\n        const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');\n        let desc = `Crea comanda collegamento arrivo per ${totalSelectedCount} cavi`;\n        if (validation.issues > 0) {\n          desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n        }\n        return desc;\n      })()\n    }, {\n      id: 'create_command_certificazione',\n      label: 'Comanda Certificazione',\n      icon: /*#__PURE__*/_jsxDEV(VerifiedIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1283,\n        columnNumber: 17\n      }, this),\n      action: 'create_command_certificazione',\n      onClick: handleContextMenuAction,\n      color: (() => {\n        const validation = getQuickValidationStatus('CERTIFICAZIONE');\n        return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n      })(),\n      description: (() => {\n        const validation = getQuickValidationStatus('CERTIFICAZIONE');\n        let desc = `Crea comanda certificazione per ${totalSelectedCount} cavi`;\n        if (validation.issues > 0) {\n          desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n        }\n        return desc;\n      })()\n    }, {\n      type: 'divider'\n    },\n    // Sezione gestione comande esistenti per selezione multipla\n    {\n      type: 'header',\n      label: `📋 Gestione Comande Esistenti (${totalSelectedCount} cavi)`\n    }, {\n      id: 'add_multiple_to_command',\n      label: 'Aggiungi Tutti a Comanda',\n      icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1310,\n        columnNumber: 17\n      }, this),\n      action: 'add_multiple_to_command',\n      onClick: handleContextMenuAction,\n      color: 'primary',\n      description: `Aggiungi tutti i ${totalSelectedCount} cavi selezionati a una comanda esistente`\n    }, {\n      id: 'remove_multiple_from_commands',\n      label: 'Rimuovi Tutti dalle Comande',\n      icon: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1319,\n        columnNumber: 17\n      }, this),\n      action: 'remove_multiple_from_commands',\n      onClick: handleContextMenuAction,\n      color: 'warning',\n      description: `Rimuovi tutti i ${totalSelectedCount} cavi selezionati dalle loro comande attuali`\n    }, {\n      type: 'divider'\n    }] : []),\n    // Azioni singolo cavo (solo se non c'è selezione multipla)\n    ...(!hasMultipleSelection ? [{\n      id: 'view_details',\n      label: 'Visualizza Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1334,\n        columnNumber: 17\n      }, this),\n      action: 'view_details',\n      onClick: handleContextMenuAction\n    }, {\n      type: 'divider'\n    },\n    // Sezione gestione comande per singolo cavo\n    {\n      type: 'header',\n      label: '📋 Gestione Comande'\n    }, {\n      id: 'add_to_command',\n      label: 'Aggiungi a Comanda',\n      icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1349,\n        columnNumber: 17\n      }, this),\n      action: 'add_to_command',\n      onClick: handleContextMenuAction,\n      color: 'primary',\n      description: 'Aggiungi questo cavo a una comanda esistente'\n    }, ...(cavo !== null && cavo !== void 0 && cavo.comanda_posa || cavo !== null && cavo !== void 0 && cavo.comanda_partenza || cavo !== null && cavo !== void 0 && cavo.comanda_arrivo || cavo !== null && cavo !== void 0 && cavo.comanda_certificazione ? [{\n      id: 'remove_from_command',\n      label: 'Rimuovi da Comanda',\n      icon: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1359,\n        columnNumber: 19\n      }, this),\n      action: 'remove_from_command',\n      onClick: handleContextMenuAction,\n      color: 'warning',\n      description: `Rimuovi da comanda ${(cavo === null || cavo === void 0 ? void 0 : cavo.comanda_posa) || (cavo === null || cavo === void 0 ? void 0 : cavo.comanda_partenza) || (cavo === null || cavo === void 0 ? void 0 : cavo.comanda_arrivo) || (cavo === null || cavo === void 0 ? void 0 : cavo.comanda_certificazione)}`\n    }] : []), {\n      type: 'divider'\n    }, {\n      id: 'edit',\n      label: 'Modifica',\n      icon: /*#__PURE__*/_jsxDEV(EditIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1372,\n        columnNumber: 17\n      }, this),\n      action: 'edit',\n      onClick: handleContextMenuAction,\n      color: 'primary'\n    }, {\n      id: 'delete',\n      label: 'Elimina',\n      icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1380,\n        columnNumber: 17\n      }, this),\n      action: 'delete',\n      onClick: handleContextMenuAction,\n      color: 'error'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'add_new',\n      label: 'Aggiungi nuovo cavo',\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1391,\n        columnNumber: 17\n      }, this),\n      action: 'add_new',\n      onClick: handleContextMenuAction,\n      color: 'success'\n    }, {\n      type: 'divider'\n    }] : []),\n    // Azioni di selezione (sempre presenti)\n    {\n      id: 'select',\n      label: isSelected ? 'Deseleziona' : 'Seleziona',\n      icon: /*#__PURE__*/_jsxDEV(SelectAllIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1404,\n        columnNumber: 15\n      }, this),\n      action: 'select',\n      onClick: handleContextMenuAction,\n      color: isSelected ? 'warning' : 'success'\n    },\n    // Azioni di copia (sempre presenti)\n    {\n      type: 'divider'\n    }, {\n      id: 'copy_id',\n      label: hasMultipleSelection ? 'Copia IDs Selezionati' : 'Copia ID',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1416,\n        columnNumber: 15\n      }, this),\n      action: 'copy_id',\n      onClick: handleContextMenuAction,\n      shortcut: 'Ctrl+C'\n    }, {\n      id: 'copy_details',\n      label: hasMultipleSelection ? 'Copia Dettagli Selezionati' : 'Copia Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1424,\n        columnNumber: 15\n      }, this),\n      action: 'copy_details',\n      onClick: handleContextMenuAction,\n      description: hasMultipleSelection ? 'Copia dettagli di tutti i cavi selezionati' : 'Copia ID, tipologia, sezione e metri'\n    }];\n    return baseMenuItems;\n  };\n\n  // Funzioni per gestire le azioni sui pulsanti stato\n  const handleStatusAction = async (cavo, actionType, actionLabel, comandaCode) => {\n    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');\n    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);\n    console.log('Action label:', actionLabel);\n    console.log('Codice comanda:', comandaCode);\n    if (actionType === 'insert_meters') {\n      // Apri il dialogo per inserire i metri posati\n      setInserisciMetriDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'modify_reel') {\n      // Apri il dialog completo per modificare la bobina con cavo preselezionato\n      console.log('Apertura dialog modifica bobina per cavo:', cavo.id_cavo);\n      setModificaBobinaDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'connect_cable' || actionType === 'connect_arrival' || actionType === 'connect_departure' || actionType === 'disconnect_cable' || actionType === 'manage_connections') {\n      // Verifica se il cavo è installato\n      if (cavo.stato_installazione !== 'Installato') {\n        console.log('Azione collegamenti ignorata per cavo non installato:', cavo.id_cavo);\n        showNotification('I collegamenti sono disponibili solo per cavi installati', 'info');\n        return;\n      }\n\n      // Cavo installato - apri il popup per gestire i collegamenti\n      console.log('Apertura popup collegamenti per cavo installato:', cavo.id_cavo, 'azione:', actionType);\n\n      // Usa setTimeout per evitare conflitti di stato\n      setTimeout(() => {\n        setCollegamentiDialog({\n          open: true,\n          cavo: cavo,\n          loading: false\n        });\n      }, 50);\n    } else if (actionType === 'create_certificate') {\n      // Apri il dialog per creare una certificazione usando il componente esistente\n      console.log('Apertura dialog creazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente con tutti i prerequisiti\n      if (certificazioneRef.current) {\n        certificazioneRef.current.createCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'view_certificate') {\n      // Apri il dialog per visualizzare la certificazione esistente\n      console.log('Apertura dialog visualizzazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per visualizzare\n      if (certificazioneRef.current) {\n        certificazioneRef.current.viewCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'generate_pdf') {\n      // Genera il PDF del certificato per il cavo\n      console.log('Generazione PDF certificato per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per generare il PDF\n      if (certificazioneRef.current) {\n        certificazioneRef.current.generatePdfForCavo(cavo);\n      }\n    } else if (actionType === 'view_command') {\n      // Naviga alla pagina della comanda specifica\n      console.log('🎯 Navigazione alla comanda:', comandaCode);\n      console.log('🏗️ Cantiere ID:', cantiereId);\n      if (comandaCode && cantiereId) {\n        // Usa la pagina originale delle comande\n        const comandeUrl = `/dashboard/cavi/comande?comanda=${comandaCode}`;\n        console.log('🔗 URL di navigazione:', comandeUrl);\n\n        // Naviga alla pagina comande originale\n        window.location.href = comandeUrl;\n        showNotification(`Apertura comanda ${comandaCode}...`, 'info');\n      } else {\n        console.error('❌ Impossibile navigare: comandaCode o cantiereId mancanti', {\n          comandaCode,\n          cantiereId\n        });\n        showNotification('Errore: impossibile aprire la comanda', 'error');\n      }\n    }\n  };\n\n  // Funzioni per gestire i callback del dialog modifica bobina\n  const handleModificaBobinaSuccess = message => {\n    showNotification(message, 'success');\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n  const handleModificaBobinaError = message => {\n    showNotification(message, 'error');\n  };\n\n  // Funzioni per chiudere i dialoghi\n  const handleCloseInserisciMetri = () => {\n    if (!inserisciMetriDialog.loading) {\n      setInserisciMetriDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n    }\n  };\n  const handleCloseModificaBobina = () => {\n    if (!modificaBobinaDialog.loading) {\n      setModificaBobinaDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n    }\n  };\n  const handleCloseCollegamenti = useCallback(() => {\n    if (!collegamentiDialog.loading) {\n      setCollegamentiDialog(prev => ({\n        ...prev,\n        open: false\n      }));\n    }\n  }, [collegamentiDialog.loading]);\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1571,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1573,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1576,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1572,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1570,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1583,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviInstallati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1588,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1584,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1582,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1595,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCollegati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1597,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Collegati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1600,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1596,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1594,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n          color: \"warning\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1607,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1612,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1608,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1606,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.iap >= 80 ? 'success.main' : statistics.iap >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.iap, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1629,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1619,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"IAP (Avanzamento Ponderato)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1634,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.metriInstallati, \"m installati\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1637,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1633,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1618,\n        columnNumber: 9\n      }, this), revisioneCorrente && /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: revisioneCorrente\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1647,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Revisione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1650,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1646,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1645,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1568,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1567,\n    columnNumber: 5\n  }, this);\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1670,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1676,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1678,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1678,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1679,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1679,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1680,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1680,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1681,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1681,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1683,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1683,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1677,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1687,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1689,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1689,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1690,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1690,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1691,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1691,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1692,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1692,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1693,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1693,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1688,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1675,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1698,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1700,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1700,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1701,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1702,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1703,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1703,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1704,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1704,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1699,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1707,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1709,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1709,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1710,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1710,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1711,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1711,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1712,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1712,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1713,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1713,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1714,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1714,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1715,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1715,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1716,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1716,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1708,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1697,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1674,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1673,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1722,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1721,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1669,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1736,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1737,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1738,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1735,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1753,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1754,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1755,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1752,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1749,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1760,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1759,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1748,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [renderDashboard(), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [selectionEnabled && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            size: \"small\",\n            onClick: () => setSelectionEnabled(false),\n            children: \"Disabilita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1784,\n            columnNumber: 19\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Modalit\\xE0 Selezione Attiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1794,\n              columnNumber: 19\n            }, this), \" - Clicca sui cavi per selezionarli, poi usa il \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"tasto destro\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1795,\n              columnNumber: 30\n            }, this), \" per creare comande multiple.\", getTotalSelectedCount() > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [getTotalSelectedCount(), \" cavi selezionati\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1797,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1797,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1793,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1780,\n          columnNumber: 15\n        }, this), selectionEnabled && getTotalSelectedCount() > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${getTotalSelectedCount()} cavi selezionati`,\n            color: \"primary\",\n            variant: \"filled\",\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1806,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1805,\n          columnNumber: 15\n        }, this), process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1819,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1817,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviAttivi,\n          onSelectionChange: handleCaviAttiviSelectionChange,\n          onSelectionToggle: handleSelectionToggle,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction,\n          onStatusAction: handleStatusAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1824,\n          columnNumber: 13\n        }, this), caviAttivi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1838,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1777,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1847,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1846,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviSpare,\n          onSelectionChange: handleCaviSpareSelectionChange,\n          onSelectionToggle: handleSelectionToggle,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction,\n          onStatusAction: handleStatusAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1851,\n          columnNumber: 13\n        }, this), caviSpare.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1864,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1845,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(InserisciMetriDialogCompleto, {\n        open: inserisciMetriDialog.open,\n        onClose: handleCloseInserisciMetri,\n        cavo: inserisciMetriDialog.cavo,\n        cantiereId: cantiereId,\n        onSuccess: message => {\n          showNotification(message, 'success');\n          // Ricarica i dati per aggiornare lo stato\n          setTimeout(() => fetchCavi(true), 500);\n        },\n        onError: message => {\n          showNotification(message, 'error');\n        },\n        loading: inserisciMetriDialog.loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1882,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ModificaBobinaDialogCompleto, {\n        open: modificaBobinaDialog.open,\n        onClose: handleCloseModificaBobina,\n        cavo: modificaBobinaDialog.cavo,\n        cantiereId: cantiereId,\n        onSuccess: handleModificaBobinaSuccess,\n        onError: handleModificaBobinaError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1898,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: collegamentiDialog.open,\n        onClose: handleCloseCollegamenti,\n        maxWidth: \"md\",\n        fullWidth: true,\n        disableEscapeKeyDown: false,\n        keepMounted: false,\n        disablePortal: false,\n        disableScrollLock: true,\n        hideBackdrop: false,\n        disableAutoFocus: true,\n        disableEnforceFocus: true,\n        disableRestoreFocus: true,\n        transitionDuration: 0,\n        TransitionProps: {\n          timeout: 0,\n          appear: false,\n          enter: false,\n          exit: false\n        },\n        PaperProps: {\n          style: {\n            transition: 'none',\n            transform: 'none'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: collegamentiDialog.cavo && /*#__PURE__*/_jsxDEV(CollegamentiCavo, {\n            cantiereId: cantiereId,\n            selectedCavo: collegamentiDialog.cavo,\n            onSuccess: message => {\n              if (message) {\n                showNotification(message, 'success');\n                // Chiudi il dialog immediatamente\n                setCollegamentiDialog(prev => ({\n                  ...prev,\n                  open: false\n                }));\n                // Ricarica i dati per aggiornare lo stato dei collegamenti\n                setTimeout(() => fetchCavi(true), 300);\n              }\n              // Non chiudere il dialog se message è null (annullamento)\n            },\n            onError: message => {\n              showNotification(message, 'error');\n            },\n            onClose: handleCloseCollegamenti\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1937,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1935,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1908,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n        open: createCommandDialog.open,\n        onClose: handleCloseCreateCommand,\n        onSuccess: handleCreateCommandSuccess,\n        cantiereId: cantiereId,\n        tipoComandaPreselezionato: createCommandDialog.tipoComanda,\n        caviPreselezionati: createCommandDialog.caviSelezionati\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1960,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'none'\n        },\n        children: /*#__PURE__*/_jsxDEV(CertificazioneCaviImproved, {\n          ref: certificazioneRef,\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            showNotification(message, 'success');\n            // Ricarica i dati per aggiornare lo stato di certificazione\n            setTimeout(() => fetchCavi(true), 500);\n          },\n          onError: message => {\n            showNotification(message, 'error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1971,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1970,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: addToCommandDialog.open,\n        onClose: () => setAddToCommandDialog({\n          open: false,\n          cavo: null,\n          comande: [],\n          loading: false,\n          isMultiple: false,\n          selectedCavi: []\n        }),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: addToCommandDialog.isMultiple ? 'Aggiungi Cavi a Comanda' : 'Aggiungi Cavo a Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1999,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: addToCommandDialog.cavo && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              pt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: addToCommandDialog.isMultiple ? 'Cavi:' : 'Cavo:'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2006,\n                columnNumber: 21\n              }, this), \" \", addToCommandDialog.cavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2005,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 3\n              },\n              children: [\"Seleziona la comanda a cui aggiungere \", addToCommandDialog.isMultiple ? 'questi cavi' : 'questo cavo', \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2008,\n              columnNumber: 19\n            }, this), addToCommandDialog.loading ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                py: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2014,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2013,\n              columnNumber: 21\n            }, this) : addToCommandDialog.comande.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              children: \"Nessuna comanda disponibile per questo cavo.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2017,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 1\n              },\n              children: addToCommandDialog.comande.map(comanda => /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: () => handleConfirmAddToCommand(comanda),\n                sx: {\n                  justifyContent: 'flex-start',\n                  textAlign: 'left',\n                  p: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    fontWeight: \"bold\",\n                    children: comanda.codice_comanda\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2034,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [comanda.tipo_comanda, \" \\u2022 \", comanda.responsabile, \" \\u2022 \", comanda.stato]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2037,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2033,\n                  columnNumber: 27\n                }, this)\n              }, comanda.codice_comanda, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2023,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2021,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2004,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2002,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setAddToCommandDialog({\n              open: false,\n              cavo: null,\n              comande: [],\n              loading: false,\n              isMultiple: false,\n              selectedCavi: []\n            }),\n            disabled: addToCommandDialog.loading,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2049,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2048,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1986,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: removeFromCommandDialog.open,\n        onClose: () => setRemoveFromCommandDialog({\n          open: false,\n          cavo: null,\n          comandaCorrente: null,\n          loading: false\n        }),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Rimuovi Cavo da Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2072,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: removeFromCommandDialog.cavo && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              pt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cavo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2079,\n                columnNumber: 21\n              }, this), \" \", removeFromCommandDialog.cavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2078,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Comanda:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2082,\n                columnNumber: 21\n              }, this), \" \", removeFromCommandDialog.comandaCorrente]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2081,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: \"Sei sicuro di voler rimuovere questo cavo dalla comanda? Questa azione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2084,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2077,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2075,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setRemoveFromCommandDialog({\n              open: false,\n              cavo: null,\n              comandaCorrente: null,\n              loading: false\n            }),\n            disabled: removeFromCommandDialog.loading,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2092,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleConfirmRemoveFromCommand,\n            color: \"warning\",\n            variant: \"contained\",\n            disabled: removeFromCommandDialog.loading,\n            children: removeFromCommandDialog.loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2104,\n              columnNumber: 52\n            }, this) : 'Rimuovi'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2098,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2091,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2066,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 4000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2116,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2110,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1770,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1733,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"ZVWQpjLpdusz9020N26MhfIIw9w=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "InfoIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Schedule", "ScheduleIcon", "Link", "LinkIcon", "<PERSON><PERSON><PERSON>", "LinkOffIcon", "Timeline", "TimelineIcon", "CheckBox", "CheckBoxIcon", "CheckBoxOutlineBlank", "CheckBoxOutlineBlankIcon", "Visibility", "VisibilityIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "SelectAll", "SelectAllIcon", "ContentCopy", "CopyIcon", "Settings", "SettingsIcon", "Verified", "VerifiedIcon", "Build", "BuildIcon", "Assignment", "AssignmentIcon", "Remove", "RemoveIcon", "useNavigate", "useAuth", "useGlobalContext", "caviService", "parcoCaviService", "CavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "InserisciMetriDialogCompleto", "ModificaBobinaDialogCompleto", "CollegamentiCavo", "CertificazioneCaviImproved", "CreaComandaConCavi", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "selectionEnabled", "setSelectionEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSele<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCaviSpare", "setSelectedCaviSpare", "inserisciMetriDialog", "setInserisciMetriDialog", "cavo", "collegamentiDialog", "setCollegamentiDialog", "modificaBobinaDialog", "setModificaBobinaDialog", "certificazioneRef", "createCommandDialog", "setCreateCommandDialog", "tipoComanda", "caviSelezionati", "statistics", "setStatistics", "totaleCavi", "caviInstallati", "caviDaInstallare", "caviInCorso", "caviCollegati", "caviNonCollegati", "caviCertificati", "caviNonCertificati", "percentualeInstallazione", "percentualeCollegamento", "percentualeCertificazione", "iap", "metriTotali", "metriInstallati", "metriR<PERSON><PERSON><PERSON>", "revisioneCorrente", "setRevisioneCorrente", "calculateIAP", "nTot", "nInst", "nColl", "nCert", "Wp", "Wc", "Wz", "sforzoSoloInstallati", "sforzoSoloCollegati", "sforzoCertificati", "numeratore", "denominatore", "console", "log", "pesi", "Math", "round", "calculateStatistics", "caviAttiviData", "caviSpareData", "tuttiCavi", "length", "totale", "filter", "stato_installazione", "colle<PERSON>nti", "responsabile_partenza", "responsabile_arrivo", "certificato", "reduce", "sum", "parseFloat", "metri_te<PERSON>ci", "metratura_reale", "newStatistics", "loadStatiInstallazione", "setStatiInstallazione", "loadRevisioneCorrente", "cantiereIdToUse", "revisioneCorrenteData", "getRevisioneCorrente", "revisione_corrente", "filters", "setFilters", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "silentLoading", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "attiviError", "caviSpareTra<PERSON>ttivi", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "handleCloseNotification", "prev", "showNotification", "handleSelectionToggle", "handleCaviAttiviSelectionChange", "selectedIds", "handleCaviSpareSelectionChange", "getAllSelectedCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id_cavo", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "getTotalSelectedCount", "handleContextMenuAction", "action", "some", "isSelected", "id", "totalSelectedCount", "allSelectedCavi", "allIds", "navigator", "clipboard", "writeText", "totalSelected", "allDetails", "sezione", "details", "handleCreateMultipleCommand", "handleAddToCommand", "handleRemoveFromCommand", "handleAddMultipleToCommand", "handleRemoveMultipleFromCommands", "getQuickValidationStatus", "valid", "issues", "errors", "warnings", "handleCloseCreateCommand", "handleCreateCommandSuccess", "codice_comanda", "handleCreateCommandError", "addToCommandDialog", "setAddToCommandDialog", "comande", "removeFromCommandDialog", "setRemoveFromCommandDialog", "comandaCorrente", "comandeService", "default", "getComande", "comandeArray", "Array", "isArray", "comandeDisponibili", "comanda", "stato", "comanda_posa", "comanda_partenza", "comanda_arrivo", "comanda_certificazione", "handleConfirmAddToCommand", "comandaSelezionata", "isMultiple", "<PERSON><PERSON><PERSON>", "listaIdCavi", "assegnaCaviAComanda", "handleConfirmRemoveFromCommand", "successCount", "errorCount", "rimuoviCavoDaComanda", "caviConComande", "getContextMenuItems", "hasMultipleSelection", "baseMenuItems", "type", "label", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "color", "validation", "description", "desc", "shortcut", "handleStatusAction", "actionType", "actionLabel", "comandaCode", "current", "createCertificationForCavo", "viewCertificationForCavo", "generatePdfForCavo", "comandeUrl", "href", "handleModificaBobinaSuccess", "handleModificaBobinaError", "handleCloseInserisciMetri", "handleCloseModificaBobina", "handleCloseCollegamenti", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "renderDetailsDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "dividers", "container", "item", "xs", "md", "gutterBottom", "sistema", "utility", "colore_cavo", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "id_bobina", "responsabile_posa", "Date", "timestamp", "toLocaleString", "className", "flexDirection", "mt", "size", "gap", "process", "env", "NODE_ENV", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "onSelectionChange", "onSelectionToggle", "contextMenuItems", "onContextMenuAction", "onStatusAction", "onSuccess", "onError", "disableEscapeKeyDown", "keepMounted", "disable<PERSON><PERSON><PERSON>", "disableScrollLock", "hideBackdrop", "disableAutoFocus", "disableEnforceFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "TransitionProps", "timeout", "appear", "enter", "exit", "PaperProps", "style", "transition", "transform", "tipoComandaPreselezionato", "caviPreselezionati", "ref", "pt", "py", "textAlign", "tipo_comanda", "responsabile", "disabled", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Snackbar,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport {\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Schedule as ScheduleIcon,\n  Link as LinkIcon,\n  LinkOff as LinkOffIcon,\n  Timeline as TimelineIcon,\n  CheckBox as CheckBoxIcon,\n  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,\n  Visibility as VisibilityIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon,\n  SelectAll as SelectAllIcon,\n  ContentCopy as CopyIcon,\n  Settings as SettingsIcon,\n  Verified as VerifiedIcon,\n  Build as BuildIcon,\n  Assignment as AssignmentIcon,\n  Remove as RemoveIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\n// import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti'; // OBSOLETO: Componente eliminato\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialogCompleto from '../../components/cavi/InserisciMetriDialogCompleto';\nimport ModificaBobinaDialogCompleto from '../../components/cavi/ModificaBobinaDialogCompleto';\nimport CollegamentiCavo from '../../components/cavi/CollegamentiCavo';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport CreaComandaConCavi from '../../components/comande/CreaComandaConCavi';\n// import comandeValidationService from '../../services/comandeValidationService';\n\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per i dialoghi di azione sui pulsanti stato\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  const [collegamentiDialog, setCollegamentiDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  // Ref per il componente CertificazioneCavi\n  const certificazioneRef = useRef(null);\n\n  // Stati per il dialog di creazione comande multiple\n  const [createCommandDialog, setCreateCommandDialog] = useState({\n    open: false,\n    tipoComanda: '',\n    caviSelezionati: [],\n    loading: false\n  });\n\n\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    iap: 0, // Indice di Avanzamento Ponderato\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n\n\n\n\n  // Stato per la revisione corrente (solo per visualizzazione nelle statistiche)\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare l'Indice di Avanzamento Ponderato (IAP)\n  const calculateIAP = (nTot, nInst, nColl, nCert) => {\n    // Pesi per le fasi del progetto\n    const Wp = 2.0;  // Peso fase Posa\n    const Wc = 1.5;  // Peso fase Collegamento\n    const Wz = 0.5;  // Peso fase Certificazione\n\n    // Se non ci sono cavi, ritorna 0\n    if (nTot === 0) return 0;\n\n    // Calcolo del numeratore (Sforzo Completato)\n    const sforzoSoloInstallati = (nInst - nColl) * Wp;\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc);\n    const sforzoCertificati = nCert * (Wp + Wc + Wz);\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati;\n\n    // Calcolo del denominatore (Sforzo Massimo Previsto)\n    const denominatore = nTot * (Wp + Wc + Wz);\n\n    // Calcolo finale dell'IAP in percentuale\n    const iap = (numeratore / denominatore) * 100;\n\n    console.log('Calcolo IAP:', {\n      nTot, nInst, nColl, nCert,\n      pesi: { Wp, Wc, Wz },\n      sforzoSoloInstallati,\n      sforzoSoloCollegati,\n      sforzoCertificati,\n      numeratore,\n      denominatore,\n      iap: Math.round(iap * 100) / 100\n    });\n\n    return Math.round(iap * 100) / 100; // Arrotonda a 2 decimali\n  };\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: caviAttiviData?.length || 0,\n      caviSpare: caviSpareData?.length || 0,\n      totale: tuttiCavi.length\n    });\n\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Installato' ||\n      cavo.stato_installazione === 'INSTALLATO' ||\n      cavo.stato_installazione === 'POSATO'\n    ).length;\n\n    const caviDaInstallare = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Da installare' ||\n      cavo.stato_installazione === 'DA_INSTALLARE'\n    ).length;\n\n    const caviInCorso = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'In corso' ||\n      cavo.stato_installazione === 'IN_CORSO'\n    ).length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo =>\n      cavo.collegamenti === 3 &&\n      cavo.responsabile_partenza &&\n      cavo.responsabile_arrivo\n    ).length;\n\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola certificazioni basandosi sul campo 'certificato' dei cavi\n    const caviCertificati = tuttiCavi.filter(cavo => cavo.certificato === true || cavo.certificato === 'true').length;\n\n    // Calcola l'Indice di Avanzamento Ponderato (IAP)\n    const iap = calculateIAP(totaleCavi, caviInstallati, caviCollegati, caviCertificati);\n\n    // Calcola percentuali tradizionali per confronto\n    const percentualeInstallazione = iap; // Sostituito con IAP\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round((caviCollegati / totaleCavi) * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi\n      .filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO')\n      .reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      percentualeCertificazione,\n      iap, // Indice di Avanzamento Ponderato\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare solo la revisione corrente (per le statistiche)\n  const loadRevisioneCorrente = async (cantiereIdToUse) => {\n    try {\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      // Il servizio restituisce un oggetto con la proprietà revisione_corrente\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n    } catch (error) {\n      console.error('Errore nel caricamento della revisione corrente:', error);\n      // Non bloccare l'applicazione se la revisione non è disponibile\n    }\n  };\n\n\n\n\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica la revisione corrente per le statistiche\n        await loadRevisioneCorrente(cantiereIdNum);\n\n\n\n\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n\n  const handleCaviAttiviSelectionChange = (selectedIds) => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n\n  const handleCaviSpareSelectionChange = (selectedIds) => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Funzionalità di modifica cavo non ancora implementata\n        showNotification('Funzionalità di modifica cavo in sviluppo', 'info');\n        break;\n      case 'delete':\n        // Funzionalità di eliminazione cavo non ancora implementata\n        showNotification('Funzionalità di eliminazione cavo in sviluppo', 'info');\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        const totalSelectedCount = getTotalSelectedCount();\n        if (totalSelectedCount > 1) {\n          // Copia tutti gli ID dei cavi selezionati\n          const allSelectedCavi = getAllSelectedCavi();\n          const allIds = allSelectedCavi.map(c => c.id_cavo).join(', ');\n          navigator.clipboard.writeText(allIds);\n          showNotification(`${totalSelectedCount} IDs cavi copiati negli appunti`, 'success');\n        } else {\n          navigator.clipboard.writeText(cavo.id_cavo);\n          showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        }\n        break;\n      case 'copy_details':\n        const totalSelected = getTotalSelectedCount();\n        if (totalSelected > 1) {\n          // Copia dettagli di tutti i cavi selezionati\n          const allSelectedCavi = getAllSelectedCavi();\n          const allDetails = allSelectedCavi.map(c =>\n            `ID: ${c.id_cavo}\\nTipologia: ${c.tipologia}\\nSezione: ${c.sezione}\\nMetri: ${c.metri_teorici}`\n          ).join('\\n\\n');\n          navigator.clipboard.writeText(allDetails);\n          showNotification(`Dettagli di ${totalSelected} cavi copiati negli appunti`, 'success');\n        } else {\n          const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n          navigator.clipboard.writeText(details);\n          showNotification('Dettagli cavo copiati negli appunti', 'success');\n        }\n        break;\n      case 'add_new':\n        // Funzionalità di aggiunta cavo non ancora implementata\n        showNotification('Funzionalità di aggiunta cavo in sviluppo', 'info');\n        break;\n      // Nuove azioni per la creazione di comande multiple\n      case 'create_command_posa':\n        handleCreateMultipleCommand('POSA');\n        break;\n      case 'create_command_collegamento_partenza':\n        handleCreateMultipleCommand('COLLEGAMENTO_PARTENZA');\n        break;\n      case 'create_command_collegamento_arrivo':\n        handleCreateMultipleCommand('COLLEGAMENTO_ARRIVO');\n        break;\n      case 'create_command_certificazione':\n        handleCreateMultipleCommand('CERTIFICAZIONE');\n        break;\n      // Nuove azioni per gestione comande singolo cavo\n      case 'add_to_command':\n        handleAddToCommand(cavo);\n        break;\n      case 'remove_from_command':\n        handleRemoveFromCommand(cavo);\n        break;\n      // Nuove azioni per gestione comande multiple\n      case 'add_multiple_to_command':\n        handleAddMultipleToCommand();\n        break;\n      case 'remove_multiple_from_commands':\n        handleRemoveMultipleFromCommands();\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Funzione per controllare rapidamente la validazione di un tipo di comanda\n  const getQuickValidationStatus = (tipoComanda) => {\n    // Temporaneamente disabilitato per debug - restituisce sempre valido\n    return { valid: true, issues: 0, errors: 0, warnings: 0 };\n  };\n\n  // Funzione per gestire la creazione di comande multiple\n  const handleCreateMultipleCommand = (tipoComanda) => {\n    const allSelectedCavi = getAllSelectedCavi();\n\n    if (allSelectedCavi.length === 0) {\n      showNotification('Nessun cavo selezionato per la creazione della comanda', 'warning');\n      return;\n    }\n\n    console.log(`Creazione comanda ${tipoComanda} per ${allSelectedCavi.length} cavi:`, allSelectedCavi.map(c => c.id_cavo));\n\n    // Apri il dialog di creazione comanda con i cavi preselezionati\n    setCreateCommandDialog({\n      open: true,\n      tipoComanda: tipoComanda,\n      caviSelezionati: allSelectedCavi,\n      loading: false\n    });\n  };\n\n  // Funzione per chiudere il dialog di creazione comande\n  const handleCloseCreateCommand = () => {\n    setCreateCommandDialog({\n      open: false,\n      tipoComanda: '',\n      caviSelezionati: [],\n      loading: false\n    });\n  };\n\n  // Funzione per gestire il successo della creazione comanda\n  const handleCreateCommandSuccess = (response) => {\n    showNotification(`Comanda ${response.codice_comanda} creata con successo!`, 'success');\n\n    // Deseleziona tutti i cavi\n    setSelectedCaviAttivi([]);\n    setSelectedCaviSpare([]);\n\n    // Chiudi il dialog\n    handleCloseCreateCommand();\n\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n\n  // Funzione per gestire gli errori nella creazione comanda\n  const handleCreateCommandError = (error) => {\n    console.error('Errore nella creazione della comanda:', error);\n    showNotification('Errore nella creazione della comanda', 'error');\n  };\n\n  // Stati per i dialog di gestione comande singolo cavo\n  const [addToCommandDialog, setAddToCommandDialog] = useState({\n    open: false,\n    cavo: null,\n    comande: [],\n    loading: false\n  });\n\n  const [removeFromCommandDialog, setRemoveFromCommandDialog] = useState({\n    open: false,\n    cavo: null,\n    comandaCorrente: null,\n    loading: false\n  });\n\n  // Funzione per aggiungere un cavo a una comanda esistente\n  const handleAddToCommand = async (cavo) => {\n    console.log('🔄 Aggiunta cavo a comanda:', cavo.id_cavo);\n    console.log('🏗️ Cantiere ID:', cantiereId);\n\n    try {\n      console.log('📋 Apertura dialog aggiunta comanda...');\n      setAddToCommandDialog({\n        open: true,\n        cavo: cavo,\n        comande: [],\n        loading: true\n      });\n\n      // Carica le comande disponibili per il cantiere\n      console.log('📡 Caricamento comande dal servizio...');\n      const comandeService = await import('../../services/comandeService');\n      const comande = await comandeService.default.getComande(cantiereId);\n      console.log('📋 Comande ricevute:', comande);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comande)) {\n        comandeArray = comande;\n      } else if (comande && Array.isArray(comande.comande)) {\n        comandeArray = comande.comande;\n      } else if (comande && Array.isArray(comande.data)) {\n        comandeArray = comande.data;\n      }\n\n      console.log('📋 Array comande processato:', comandeArray);\n\n      // Filtra le comande che possono accettare questo cavo\n      const comandeDisponibili = comandeArray.filter(comanda => {\n        // Escludi comande completate o cancellate\n        return comanda.stato !== 'COMPLETATA' && comanda.stato !== 'CANCELLATA';\n      });\n\n      console.log('📋 Comande disponibili filtrate:', comandeDisponibili);\n\n      setAddToCommandDialog({\n        open: true,\n        cavo: cavo,\n        comande: comandeDisponibili,\n        loading: false\n      });\n\n    } catch (error) {\n      console.error('❌ Errore nel caricamento delle comande:', error);\n      showNotification('Errore nel caricamento delle comande disponibili', 'error');\n      setAddToCommandDialog({\n        open: false,\n        cavo: null,\n        comande: [],\n        loading: false\n      });\n    }\n  };\n\n  // Funzione per rimuovere un cavo da una comanda\n  const handleRemoveFromCommand = (cavo) => {\n    console.log('🔄 Rimozione cavo da comanda:', cavo.id_cavo);\n\n    // Determina la comanda corrente del cavo\n    const comandaCorrente = cavo.comanda_posa || cavo.comanda_partenza ||\n                           cavo.comanda_arrivo || cavo.comanda_certificazione;\n\n    if (!comandaCorrente) {\n      showNotification('Il cavo non è assegnato a nessuna comanda', 'warning');\n      return;\n    }\n\n    setRemoveFromCommandDialog({\n      open: true,\n      cavo: cavo,\n      comandaCorrente: comandaCorrente,\n      loading: false\n    });\n  };\n\n  // Funzione per confermare l'aggiunta del cavo/cavi alla comanda\n  const handleConfirmAddToCommand = async (comandaSelezionata) => {\n    try {\n      setAddToCommandDialog(prev => ({ ...prev, loading: true }));\n\n      const comandeService = await import('../../services/comandeService');\n\n      if (addToCommandDialog.isMultiple && addToCommandDialog.selectedCavi) {\n        // Modalità multipla: aggiungi tutti i cavi selezionati\n        const listaIdCavi = addToCommandDialog.selectedCavi.map(cavo => cavo.id_cavo);\n\n        await comandeService.default.assegnaCaviAComanda(\n          comandaSelezionata.codice_comanda,\n          listaIdCavi\n        );\n\n        showNotification(\n          `${listaIdCavi.length} cavi aggiunti alla comanda ${comandaSelezionata.codice_comanda}`,\n          'success'\n        );\n\n        // Deseleziona tutti i cavi\n        setSelectedCaviAttivi([]);\n        setSelectedCaviSpare([]);\n\n      } else {\n        // Modalità singola: aggiungi solo il cavo corrente\n        await comandeService.default.assegnaCaviAComanda(\n          comandaSelezionata.codice_comanda,\n          [addToCommandDialog.cavo.id_cavo]\n        );\n\n        showNotification(\n          `Cavo ${addToCommandDialog.cavo.id_cavo} aggiunto alla comanda ${comandaSelezionata.codice_comanda}`,\n          'success'\n        );\n      }\n\n      // Chiudi il dialog\n      setAddToCommandDialog({\n        open: false,\n        cavo: null,\n        comande: [],\n        loading: false,\n        isMultiple: false,\n        selectedCavi: []\n      });\n\n      // Ricarica i dati\n      setTimeout(() => fetchCavi(true), 500);\n\n    } catch (error) {\n      console.error('Errore nell\\'aggiunta del cavo alla comanda:', error);\n      showNotification('Errore nell\\'aggiunta del cavo alla comanda', 'error');\n      setAddToCommandDialog(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  // Funzione per confermare la rimozione del cavo/cavi dalla comanda\n  const handleConfirmRemoveFromCommand = async () => {\n    try {\n      setRemoveFromCommandDialog(prev => ({ ...prev, loading: true }));\n\n      const comandeService = await import('../../services/comandeService');\n\n      if (removeFromCommandDialog.isMultiple && removeFromCommandDialog.selectedCavi) {\n        // Modalità multipla: rimuovi tutti i cavi selezionati dalle loro comande\n        let successCount = 0;\n        let errorCount = 0;\n\n        for (const cavo of removeFromCommandDialog.selectedCavi) {\n          try {\n            const comandaCorrente = cavo.comanda_posa || cavo.comanda_partenza ||\n                                   cavo.comanda_arrivo || cavo.comanda_certificazione;\n\n            if (comandaCorrente) {\n              await comandeService.default.rimuoviCavoDaComanda(comandaCorrente, cavo.id_cavo);\n              successCount++;\n            }\n          } catch (error) {\n            console.error(`Errore nella rimozione del cavo ${cavo.id_cavo}:`, error);\n            errorCount++;\n          }\n        }\n\n        if (successCount > 0) {\n          showNotification(\n            `${successCount} cavi rimossi dalle comande${errorCount > 0 ? ` (${errorCount} errori)` : ''}`,\n            errorCount > 0 ? 'warning' : 'success'\n          );\n        } else {\n          showNotification('Errore nella rimozione dei cavi dalle comande', 'error');\n        }\n\n        // Deseleziona tutti i cavi\n        setSelectedCaviAttivi([]);\n        setSelectedCaviSpare([]);\n\n      } else {\n        // Modalità singola: rimuovi solo il cavo corrente\n        await comandeService.default.rimuoviCavoDaComanda(\n          removeFromCommandDialog.comandaCorrente,\n          removeFromCommandDialog.cavo.id_cavo\n        );\n\n        showNotification(\n          `Cavo ${removeFromCommandDialog.cavo.id_cavo} rimosso dalla comanda ${removeFromCommandDialog.comandaCorrente}`,\n          'success'\n        );\n      }\n\n      // Chiudi il dialog\n      setRemoveFromCommandDialog({\n        open: false,\n        cavo: null,\n        comandaCorrente: null,\n        loading: false,\n        isMultiple: false,\n        selectedCavi: []\n      });\n\n      // Ricarica i dati\n      setTimeout(() => fetchCavi(true), 500);\n\n    } catch (error) {\n      console.error('Errore nella rimozione del cavo dalla comanda:', error);\n      showNotification('Errore nella rimozione del cavo dalla comanda', 'error');\n      setRemoveFromCommandDialog(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  // Funzione per aggiungere più cavi a una comanda esistente\n  const handleAddMultipleToCommand = async () => {\n    const allSelectedCavi = getAllSelectedCavi();\n\n    if (allSelectedCavi.length === 0) {\n      showNotification('Nessun cavo selezionato', 'warning');\n      return;\n    }\n\n    console.log('🔄 Aggiunta multipla cavi a comanda:', allSelectedCavi.map(c => c.id_cavo));\n\n    try {\n      console.log('📋 Apertura dialog aggiunta multipla comanda...');\n      setAddToCommandDialog({\n        open: true,\n        cavo: { id_cavo: `${allSelectedCavi.length} cavi selezionati` }, // Placeholder per il dialog\n        comande: [],\n        loading: true,\n        isMultiple: true,\n        selectedCavi: allSelectedCavi\n      });\n\n      // Carica le comande disponibili per il cantiere\n      console.log('📡 Caricamento comande dal servizio...');\n      const comandeService = await import('../../services/comandeService');\n      const comande = await comandeService.default.getComande(cantiereId);\n      console.log('📋 Comande ricevute:', comande);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comande)) {\n        comandeArray = comande;\n      } else if (comande && Array.isArray(comande.comande)) {\n        comandeArray = comande.comande;\n      } else if (comande && Array.isArray(comande.data)) {\n        comandeArray = comande.data;\n      }\n\n      // Filtra le comande che possono accettare questi cavi\n      const comandeDisponibili = comandeArray.filter(comanda => {\n        return comanda.stato !== 'COMPLETATA' && comanda.stato !== 'CANCELLATA';\n      });\n\n      setAddToCommandDialog({\n        open: true,\n        cavo: { id_cavo: `${allSelectedCavi.length} cavi selezionati` },\n        comande: comandeDisponibili,\n        loading: false,\n        isMultiple: true,\n        selectedCavi: allSelectedCavi\n      });\n\n    } catch (error) {\n      console.error('❌ Errore nel caricamento delle comande:', error);\n      showNotification('Errore nel caricamento delle comande disponibili', 'error');\n      setAddToCommandDialog({\n        open: false,\n        cavo: null,\n        comande: [],\n        loading: false,\n        isMultiple: false,\n        selectedCavi: []\n      });\n    }\n  };\n\n  // Funzione per rimuovere più cavi dalle loro comande\n  const handleRemoveMultipleFromCommands = () => {\n    const allSelectedCavi = getAllSelectedCavi();\n\n    if (allSelectedCavi.length === 0) {\n      showNotification('Nessun cavo selezionato', 'warning');\n      return;\n    }\n\n    // Filtra solo i cavi che sono effettivamente assegnati a comande\n    const caviConComande = allSelectedCavi.filter(cavo =>\n      cavo.comanda_posa || cavo.comanda_partenza || cavo.comanda_arrivo || cavo.comanda_certificazione\n    );\n\n    if (caviConComande.length === 0) {\n      showNotification('Nessuno dei cavi selezionati è assegnato a una comanda', 'info');\n      return;\n    }\n\n    console.log('🔄 Rimozione multipla cavi da comande:', caviConComande.map(c => c.id_cavo));\n\n    setRemoveFromCommandDialog({\n      open: true,\n      cavo: { id_cavo: `${caviConComande.length} cavi selezionati` },\n      comandaCorrente: 'multiple',\n      loading: false,\n      isMultiple: true,\n      selectedCavi: caviConComande\n    });\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = (cavo) => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === cavo?.id_cavo)\n      ? selectedCaviAttivi.includes(cavo?.id_cavo)\n      : selectedCaviSpare.includes(cavo?.id_cavo);\n\n    const totalSelectedCount = getTotalSelectedCount();\n    const hasMultipleSelection = totalSelectedCount > 1;\n\n    // Menu base per singolo cavo\n    const baseMenuItems = [\n      {\n        type: 'header',\n        label: hasMultipleSelection ? `${totalSelectedCount} cavi selezionati` : `Cavo ${cavo?.id_cavo || ''}`\n      },\n      // Sezione comande multiple (solo se ci sono più cavi selezionati)\n      ...(hasMultipleSelection ? [\n        {\n          type: 'header',\n          label: `📋 Crea Comande Multiple (${totalSelectedCount} cavi)`\n        },\n        {\n          id: 'create_command_posa',\n          label: 'Comanda Posa',\n          icon: <BuildIcon fontSize=\"small\" />,\n          action: 'create_command_posa',\n          onClick: handleContextMenuAction,\n          color: (() => {\n            const validation = getQuickValidationStatus('POSA');\n            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n          })(),\n          description: (() => {\n            const validation = getQuickValidationStatus('POSA');\n            let desc = `Crea comanda posa per ${totalSelectedCount} cavi`;\n            if (validation.issues > 0) {\n              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n            }\n            return desc;\n          })()\n        },\n        {\n          id: 'create_command_collegamento_partenza',\n          label: 'Comanda Collegamento Partenza',\n          icon: <LinkIcon fontSize=\"small\" />,\n          action: 'create_command_collegamento_partenza',\n          onClick: handleContextMenuAction,\n          color: (() => {\n            const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');\n            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n          })(),\n          description: (() => {\n            const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');\n            let desc = `Crea comanda collegamento partenza per ${totalSelectedCount} cavi`;\n            if (validation.issues > 0) {\n              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n            }\n            return desc;\n          })()\n        },\n        {\n          id: 'create_command_collegamento_arrivo',\n          label: 'Comanda Collegamento Arrivo',\n          icon: <LinkIcon fontSize=\"small\" />,\n          action: 'create_command_collegamento_arrivo',\n          onClick: handleContextMenuAction,\n          color: (() => {\n            const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');\n            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n          })(),\n          description: (() => {\n            const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');\n            let desc = `Crea comanda collegamento arrivo per ${totalSelectedCount} cavi`;\n            if (validation.issues > 0) {\n              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n            }\n            return desc;\n          })()\n        },\n        {\n          id: 'create_command_certificazione',\n          label: 'Comanda Certificazione',\n          icon: <VerifiedIcon fontSize=\"small\" />,\n          action: 'create_command_certificazione',\n          onClick: handleContextMenuAction,\n          color: (() => {\n            const validation = getQuickValidationStatus('CERTIFICAZIONE');\n            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n          })(),\n          description: (() => {\n            const validation = getQuickValidationStatus('CERTIFICAZIONE');\n            let desc = `Crea comanda certificazione per ${totalSelectedCount} cavi`;\n            if (validation.issues > 0) {\n              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n            }\n            return desc;\n          })()\n        },\n        {\n          type: 'divider'\n        },\n        // Sezione gestione comande esistenti per selezione multipla\n        {\n          type: 'header',\n          label: `📋 Gestione Comande Esistenti (${totalSelectedCount} cavi)`\n        },\n        {\n          id: 'add_multiple_to_command',\n          label: 'Aggiungi Tutti a Comanda',\n          icon: <AssignmentIcon fontSize=\"small\" />,\n          action: 'add_multiple_to_command',\n          onClick: handleContextMenuAction,\n          color: 'primary',\n          description: `Aggiungi tutti i ${totalSelectedCount} cavi selezionati a una comanda esistente`\n        },\n        {\n          id: 'remove_multiple_from_commands',\n          label: 'Rimuovi Tutti dalle Comande',\n          icon: <RemoveIcon fontSize=\"small\" />,\n          action: 'remove_multiple_from_commands',\n          onClick: handleContextMenuAction,\n          color: 'warning',\n          description: `Rimuovi tutti i ${totalSelectedCount} cavi selezionati dalle loro comande attuali`\n        },\n        {\n          type: 'divider'\n        }\n      ] : []),\n      // Azioni singolo cavo (solo se non c'è selezione multipla)\n      ...(!hasMultipleSelection ? [\n        {\n          id: 'view_details',\n          label: 'Visualizza Dettagli',\n          icon: <VisibilityIcon fontSize=\"small\" />,\n          action: 'view_details',\n          onClick: handleContextMenuAction\n        },\n        {\n          type: 'divider'\n        },\n        // Sezione gestione comande per singolo cavo\n        {\n          type: 'header',\n          label: '📋 Gestione Comande'\n        },\n        {\n          id: 'add_to_command',\n          label: 'Aggiungi a Comanda',\n          icon: <AssignmentIcon fontSize=\"small\" />,\n          action: 'add_to_command',\n          onClick: handleContextMenuAction,\n          color: 'primary',\n          description: 'Aggiungi questo cavo a una comanda esistente'\n        },\n        ...(cavo?.comanda_posa || cavo?.comanda_partenza || cavo?.comanda_arrivo || cavo?.comanda_certificazione ? [\n          {\n            id: 'remove_from_command',\n            label: 'Rimuovi da Comanda',\n            icon: <RemoveIcon fontSize=\"small\" />,\n            action: 'remove_from_command',\n            onClick: handleContextMenuAction,\n            color: 'warning',\n            description: `Rimuovi da comanda ${cavo?.comanda_posa || cavo?.comanda_partenza || cavo?.comanda_arrivo || cavo?.comanda_certificazione}`\n          }\n        ] : []),\n        {\n          type: 'divider'\n        },\n        {\n          id: 'edit',\n          label: 'Modifica',\n          icon: <EditIcon fontSize=\"small\" />,\n          action: 'edit',\n          onClick: handleContextMenuAction,\n          color: 'primary'\n        },\n        {\n          id: 'delete',\n          label: 'Elimina',\n          icon: <DeleteIcon fontSize=\"small\" />,\n          action: 'delete',\n          onClick: handleContextMenuAction,\n          color: 'error'\n        },\n        {\n          type: 'divider'\n        },\n        {\n          id: 'add_new',\n          label: 'Aggiungi nuovo cavo',\n          icon: <AddIcon fontSize=\"small\" />,\n          action: 'add_new',\n          onClick: handleContextMenuAction,\n          color: 'success'\n        },\n        {\n          type: 'divider'\n        }\n      ] : []),\n      // Azioni di selezione (sempre presenti)\n      {\n        id: 'select',\n        label: isSelected ? 'Deseleziona' : 'Seleziona',\n        icon: <SelectAllIcon fontSize=\"small\" />,\n        action: 'select',\n        onClick: handleContextMenuAction,\n        color: isSelected ? 'warning' : 'success'\n      },\n      // Azioni di copia (sempre presenti)\n      {\n        type: 'divider'\n      },\n      {\n        id: 'copy_id',\n        label: hasMultipleSelection ? 'Copia IDs Selezionati' : 'Copia ID',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_id',\n        onClick: handleContextMenuAction,\n        shortcut: 'Ctrl+C'\n      },\n      {\n        id: 'copy_details',\n        label: hasMultipleSelection ? 'Copia Dettagli Selezionati' : 'Copia Dettagli',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_details',\n        onClick: handleContextMenuAction,\n        description: hasMultipleSelection ? 'Copia dettagli di tutti i cavi selezionati' : 'Copia ID, tipologia, sezione e metri'\n      }\n    ];\n\n    return baseMenuItems;\n  };\n\n  // Funzioni per gestire le azioni sui pulsanti stato\n  const handleStatusAction = async (cavo, actionType, actionLabel, comandaCode) => {\n    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');\n    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);\n    console.log('Action label:', actionLabel);\n    console.log('Codice comanda:', comandaCode);\n\n    if (actionType === 'insert_meters') {\n      // Apri il dialogo per inserire i metri posati\n      setInserisciMetriDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'modify_reel') {\n      // Apri il dialog completo per modificare la bobina con cavo preselezionato\n      console.log('Apertura dialog modifica bobina per cavo:', cavo.id_cavo);\n      setModificaBobinaDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'connect_cable' || actionType === 'connect_arrival' ||\n               actionType === 'connect_departure' || actionType === 'disconnect_cable' ||\n               actionType === 'manage_connections') {\n\n      // Verifica se il cavo è installato\n      if (cavo.stato_installazione !== 'Installato') {\n        console.log('Azione collegamenti ignorata per cavo non installato:', cavo.id_cavo);\n        showNotification('I collegamenti sono disponibili solo per cavi installati', 'info');\n        return;\n      }\n\n      // Cavo installato - apri il popup per gestire i collegamenti\n      console.log('Apertura popup collegamenti per cavo installato:', cavo.id_cavo, 'azione:', actionType);\n\n      // Usa setTimeout per evitare conflitti di stato\n      setTimeout(() => {\n        setCollegamentiDialog({\n          open: true,\n          cavo: cavo,\n          loading: false\n        });\n      }, 50);\n    } else if (actionType === 'create_certificate') {\n      // Apri il dialog per creare una certificazione usando il componente esistente\n      console.log('Apertura dialog creazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente con tutti i prerequisiti\n      if (certificazioneRef.current) {\n        certificazioneRef.current.createCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'view_certificate') {\n      // Apri il dialog per visualizzare la certificazione esistente\n      console.log('Apertura dialog visualizzazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per visualizzare\n      if (certificazioneRef.current) {\n        certificazioneRef.current.viewCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'generate_pdf') {\n      // Genera il PDF del certificato per il cavo\n      console.log('Generazione PDF certificato per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per generare il PDF\n      if (certificazioneRef.current) {\n        certificazioneRef.current.generatePdfForCavo(cavo);\n      }\n    } else if (actionType === 'view_command') {\n      // Naviga alla pagina della comanda specifica\n      console.log('🎯 Navigazione alla comanda:', comandaCode);\n      console.log('🏗️ Cantiere ID:', cantiereId);\n\n      if (comandaCode && cantiereId) {\n        // Usa la pagina originale delle comande\n        const comandeUrl = `/dashboard/cavi/comande?comanda=${comandaCode}`;\n        console.log('🔗 URL di navigazione:', comandeUrl);\n\n        // Naviga alla pagina comande originale\n        window.location.href = comandeUrl;\n\n        showNotification(`Apertura comanda ${comandaCode}...`, 'info');\n      } else {\n        console.error('❌ Impossibile navigare: comandaCode o cantiereId mancanti', {\n          comandaCode,\n          cantiereId\n        });\n        showNotification('Errore: impossibile aprire la comanda', 'error');\n      }\n    }\n  };\n\n\n\n\n  // Funzioni per gestire i callback del dialog modifica bobina\n  const handleModificaBobinaSuccess = (message) => {\n    showNotification(message, 'success');\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n\n  const handleModificaBobinaError = (message) => {\n    showNotification(message, 'error');\n  };\n\n  // Funzioni per chiudere i dialoghi\n  const handleCloseInserisciMetri = () => {\n    if (!inserisciMetriDialog.loading) {\n      setInserisciMetriDialog({ open: false, cavo: null, loading: false });\n    }\n  };\n\n  const handleCloseModificaBobina = () => {\n    if (!modificaBobinaDialog.loading) {\n      setModificaBobinaDialog({ open: false, cavo: null, loading: false });\n    }\n  };\n\n  const handleCloseCollegamenti = useCallback(() => {\n    if (!collegamentiDialog.loading) {\n      setCollegamentiDialog(prev => ({ ...prev, open: false }));\n    }\n  }, [collegamentiDialog.loading]);\n\n\n\n\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckCircleIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviInstallati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Installati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <LinkIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCollegati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Collegati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <VerifiedIcon color=\"warning\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Certificati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.iap >= 80 ? 'success.main' :\n                     statistics.iap >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.iap}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              IAP (Avanzamento Ponderato)\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.metriInstallati}m installati\n            </Typography>\n          </Box>\n        </Stack>\n\n        {/* Revisione corrente */}\n        {revisioneCorrente && (\n          <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n            <Box>\n              <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                {revisioneCorrente}\n              </Typography>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Revisione\n              </Typography>\n            </Box>\n          </Stack>\n        )}\n      </Stack>\n    </Paper>\n  );\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                {/* sh field is now a spare field (kept in DB but hidden in UI) */}\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n\n\n          {/* Dashboard con statistiche avanzate */}\n          {renderDashboard()}\n\n          {/* Sezione Cavi */}\n          <Box sx={{ mt: 4 }}>\n            {/* Banner informativo per modalità selezione */}\n            {selectionEnabled && (\n              <Alert\n                severity=\"info\"\n                sx={{ mb: 2 }}\n                action={\n                  <Button\n                    color=\"inherit\"\n                    size=\"small\"\n                    onClick={() => setSelectionEnabled(false)}\n                  >\n                    Disabilita\n                  </Button>\n                }\n              >\n                <Typography variant=\"body2\">\n                  <strong>Modalità Selezione Attiva</strong> - Clicca sui cavi per selezionarli,\n                  poi usa il <strong>tasto destro</strong> per creare comande multiple.\n                  {getTotalSelectedCount() > 0 && (\n                    <span> <strong>{getTotalSelectedCount()} cavi selezionati</strong></span>\n                  )}\n                </Typography>\n              </Alert>\n            )}\n\n            {/* Contatore selezione compatto - solo quando ci sono cavi selezionati */}\n            {selectionEnabled && getTotalSelectedCount() > 0 && (\n              <Box sx={{ mb: 2 }}>\n                <Chip\n                  label={`${getTotalSelectedCount()} cavi selezionati`}\n                  color=\"primary\"\n                  variant=\"filled\"\n                  size=\"small\"\n                />\n              </Box>\n            )}\n\n            {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n            {process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && (\n              <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                {Object.keys(caviAttivi[0]).map(key => (\n                  <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                ))}\n              </Box>\n            )}\n\n            <CaviFilterableTable\n              cavi={caviAttivi}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviAttivi}\n              onSelectionChange={handleCaviAttiviSelectionChange}\n              onSelectionToggle={handleSelectionToggle}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n              onStatusAction={handleStatusAction}\n            />\n            {caviAttivi.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n            </Box>\n            <CaviFilterableTable\n              cavi={caviSpare}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviSpare}\n              onSelectionChange={handleCaviSpareSelectionChange}\n              onSelectionToggle={handleSelectionToggle}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n              onStatusAction={handleStatusAction}\n            />\n            {caviSpare.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* OBSOLETO: Dialog eliminazione cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* OBSOLETO: Dialog modifica cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* OBSOLETO: Dialog aggiunta cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* Dialoghi per azioni sui pulsanti stato */}\n          <InserisciMetriDialogCompleto\n            open={inserisciMetriDialog.open}\n            onClose={handleCloseInserisciMetri}\n            cavo={inserisciMetriDialog.cavo}\n            cantiereId={cantiereId}\n            onSuccess={(message) => {\n              showNotification(message, 'success');\n              // Ricarica i dati per aggiornare lo stato\n              setTimeout(() => fetchCavi(true), 500);\n            }}\n            onError={(message) => {\n              showNotification(message, 'error');\n            }}\n            loading={inserisciMetriDialog.loading}\n          />\n\n          <ModificaBobinaDialogCompleto\n            open={modificaBobinaDialog.open}\n            onClose={handleCloseModificaBobina}\n            cavo={modificaBobinaDialog.cavo}\n            cantiereId={cantiereId}\n            onSuccess={handleModificaBobinaSuccess}\n            onError={handleModificaBobinaError}\n          />\n\n          {/* Dialog per la gestione collegamenti */}\n          <Dialog\n            open={collegamentiDialog.open}\n            onClose={handleCloseCollegamenti}\n            maxWidth=\"md\"\n            fullWidth\n            disableEscapeKeyDown={false}\n            keepMounted={false}\n            disablePortal={false}\n            disableScrollLock={true}\n            hideBackdrop={false}\n            disableAutoFocus={true}\n            disableEnforceFocus={true}\n            disableRestoreFocus={true}\n            transitionDuration={0}\n            TransitionProps={{\n              timeout: 0,\n              appear: false,\n              enter: false,\n              exit: false\n            }}\n            PaperProps={{\n              style: {\n                transition: 'none',\n                transform: 'none'\n              }\n            }}\n          >\n            <DialogContent>\n              {collegamentiDialog.cavo && (\n                <CollegamentiCavo\n                  cantiereId={cantiereId}\n                  selectedCavo={collegamentiDialog.cavo}\n                  onSuccess={(message) => {\n                    if (message) {\n                      showNotification(message, 'success');\n                      // Chiudi il dialog immediatamente\n                      setCollegamentiDialog(prev => ({ ...prev, open: false }));\n                      // Ricarica i dati per aggiornare lo stato dei collegamenti\n                      setTimeout(() => fetchCavi(true), 300);\n                    }\n                    // Non chiudere il dialog se message è null (annullamento)\n                  }}\n                  onError={(message) => {\n                    showNotification(message, 'error');\n                  }}\n                  onClose={handleCloseCollegamenti}\n                />\n              )}\n            </DialogContent>\n          </Dialog>\n\n          {/* Dialog per la creazione di comande */}\n          <CreaComandaConCavi\n            open={createCommandDialog.open}\n            onClose={handleCloseCreateCommand}\n            onSuccess={handleCreateCommandSuccess}\n            cantiereId={cantiereId}\n            tipoComandaPreselezionato={createCommandDialog.tipoComanda}\n            caviPreselezionati={createCommandDialog.caviSelezionati}\n          />\n\n          {/* Componente CertificazioneCaviImproved per i dialog completi */}\n          <Box sx={{ display: 'none' }}>\n            <CertificazioneCaviImproved\n              ref={certificazioneRef}\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                showNotification(message, 'success');\n                // Ricarica i dati per aggiornare lo stato di certificazione\n                setTimeout(() => fetchCavi(true), 500);\n              }}\n              onError={(message) => {\n                showNotification(message, 'error');\n              }}\n            />\n          </Box>\n\n          {/* Dialog per aggiungere cavo a comanda */}\n          <Dialog\n            open={addToCommandDialog.open}\n            onClose={() => setAddToCommandDialog({\n              open: false,\n              cavo: null,\n              comande: [],\n              loading: false,\n              isMultiple: false,\n              selectedCavi: []\n            })}\n            maxWidth=\"sm\"\n            fullWidth\n          >\n            <DialogTitle>\n              {addToCommandDialog.isMultiple ? 'Aggiungi Cavi a Comanda' : 'Aggiungi Cavo a Comanda'}\n            </DialogTitle>\n            <DialogContent>\n              {addToCommandDialog.cavo && (\n                <Box sx={{ pt: 2 }}>\n                  <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                    <strong>{addToCommandDialog.isMultiple ? 'Cavi:' : 'Cavo:'}</strong> {addToCommandDialog.cavo.id_cavo}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Seleziona la comanda a cui aggiungere {addToCommandDialog.isMultiple ? 'questi cavi' : 'questo cavo'}:\n                  </Typography>\n\n                  {addToCommandDialog.loading ? (\n                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>\n                      <CircularProgress />\n                    </Box>\n                  ) : addToCommandDialog.comande.length === 0 ? (\n                    <Alert severity=\"info\">\n                      Nessuna comanda disponibile per questo cavo.\n                    </Alert>\n                  ) : (\n                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                      {addToCommandDialog.comande.map((comanda) => (\n                        <Button\n                          key={comanda.codice_comanda}\n                          variant=\"outlined\"\n                          onClick={() => handleConfirmAddToCommand(comanda)}\n                          sx={{\n                            justifyContent: 'flex-start',\n                            textAlign: 'left',\n                            p: 2\n                          }}\n                        >\n                          <Box>\n                            <Typography variant=\"body1\" fontWeight=\"bold\">\n                              {comanda.codice_comanda}\n                            </Typography>\n                            <Typography variant=\"body2\" color=\"text.secondary\">\n                              {comanda.tipo_comanda} • {comanda.responsabile} • {comanda.stato}\n                            </Typography>\n                          </Box>\n                        </Button>\n                      ))}\n                    </Box>\n                  )}\n                </Box>\n              )}\n            </DialogContent>\n            <DialogActions>\n              <Button\n                onClick={() => setAddToCommandDialog({\n                  open: false,\n                  cavo: null,\n                  comande: [],\n                  loading: false,\n                  isMultiple: false,\n                  selectedCavi: []\n                })}\n                disabled={addToCommandDialog.loading}\n              >\n                Annulla\n              </Button>\n            </DialogActions>\n          </Dialog>\n\n          {/* Dialog per rimuovere cavo da comanda */}\n          <Dialog\n            open={removeFromCommandDialog.open}\n            onClose={() => setRemoveFromCommandDialog({ open: false, cavo: null, comandaCorrente: null, loading: false })}\n            maxWidth=\"sm\"\n            fullWidth\n          >\n            <DialogTitle>\n              Rimuovi Cavo da Comanda\n            </DialogTitle>\n            <DialogContent>\n              {removeFromCommandDialog.cavo && (\n                <Box sx={{ pt: 2 }}>\n                  <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                    <strong>Cavo:</strong> {removeFromCommandDialog.cavo.id_cavo}\n                  </Typography>\n                  <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                    <strong>Comanda:</strong> {removeFromCommandDialog.comandaCorrente}\n                  </Typography>\n                  <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                    Sei sicuro di voler rimuovere questo cavo dalla comanda?\n                    Questa azione non può essere annullata.\n                  </Alert>\n                </Box>\n              )}\n            </DialogContent>\n            <DialogActions>\n              <Button\n                onClick={() => setRemoveFromCommandDialog({ open: false, cavo: null, comandaCorrente: null, loading: false })}\n                disabled={removeFromCommandDialog.loading}\n              >\n                Annulla\n              </Button>\n              <Button\n                onClick={handleConfirmRemoveFromCommand}\n                color=\"warning\"\n                variant=\"contained\"\n                disabled={removeFromCommandDialog.loading}\n              >\n                {removeFromCommandDialog.loading ? <CircularProgress size={20} /> : 'Rimuovi'}\n              </Button>\n            </DialogActions>\n          </Dialog>\n\n          {/* Snackbar per le notifiche */}\n          <Snackbar\n            open={notification.open}\n            autoHideDuration={4000}\n            onClose={handleCloseNotification}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n              {notification.message}\n            </Alert>\n          </Snackbar>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SACEC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,oBAAoB,IAAIC,wBAAwB,EAChDC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,SAAS,IAAIC,aAAa,EAC1BC,WAAW,IAAIC,QAAQ,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D;AACA,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAOC,4BAA4B,MAAM,oDAAoD;AAC7F,OAAOC,4BAA4B,MAAM,oDAAoD;AAC7F,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,0BAA0B,MAAM,kDAAkD;AACzF,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E;;AAEA,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEkB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGtB,gBAAgB,CAAC,CAAC;EACpL,MAAMuB,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC6F,YAAY,EAAEC,eAAe,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+F,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiG,SAAS,EAAEC,YAAY,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmG,OAAO,EAAEC,UAAU,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqG,KAAK,EAAEC,QAAQ,CAAC,GAAGtG,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,MAAM,CAACuG,YAAY,EAAEC,eAAe,CAAC,GAAGxG,QAAQ,CAAC;IAAEyG,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACnG;;EAEA;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACgH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkH,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACoH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrH,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA,MAAM,CAACsH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvH,QAAQ,CAAC;IAC/DyG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACsB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1H,QAAQ,CAAC;IAC3DyG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACwB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5H,QAAQ,CAAC;IAC/DyG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM0B,iBAAiB,GAAG1H,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,MAAM,CAAC2H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/H,QAAQ,CAAC;IAC7DyG,IAAI,EAAE,KAAK;IACXuB,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnB9B,OAAO,EAAE;EACX,CAAC,CAAC;;EAIF;EACA,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGnI,QAAQ,CAAC;IAC3CoI,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,uBAAuB,EAAE,CAAC;IAC1BC,yBAAyB,EAAE,CAAC;IAC5BC,GAAG,EAAE,CAAC;IAAE;IACRC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAMF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;;EAE9D;;EAEA;EACA,MAAMqJ,YAAY,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IAClD;IACA,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;IACjB,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;IACjB,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;;IAEjB;IACA,IAAIN,IAAI,KAAK,CAAC,EAAE,OAAO,CAAC;;IAExB;IACA,MAAMO,oBAAoB,GAAG,CAACN,KAAK,GAAGC,KAAK,IAAIE,EAAE;IACjD,MAAMI,mBAAmB,GAAG,CAACN,KAAK,GAAGC,KAAK,KAAKC,EAAE,GAAGC,EAAE,CAAC;IACvD,MAAMI,iBAAiB,GAAGN,KAAK,IAAIC,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;IAChD,MAAMI,UAAU,GAAGH,oBAAoB,GAAGC,mBAAmB,GAAGC,iBAAiB;;IAEjF;IACA,MAAME,YAAY,GAAGX,IAAI,IAAII,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;;IAE1C;IACA,MAAMb,GAAG,GAAIiB,UAAU,GAAGC,YAAY,GAAI,GAAG;IAE7CC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1Bb,IAAI;MAAEC,KAAK;MAAEC,KAAK;MAAEC,KAAK;MACzBW,IAAI,EAAE;QAAEV,EAAE;QAAEC,EAAE;QAAEC;MAAG,CAAC;MACpBC,oBAAoB;MACpBC,mBAAmB;MACnBC,iBAAiB;MACjBC,UAAU;MACVC,YAAY;MACZlB,GAAG,EAAEsB,IAAI,CAACC,KAAK,CAACvB,GAAG,GAAG,GAAG,CAAC,GAAG;IAC/B,CAAC,CAAC;IAEF,OAAOsB,IAAI,CAACC,KAAK,CAACvB,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAMwB,mBAAmB,GAAGA,CAACC,cAAc,EAAEC,aAAa,KAAK;IAC7D,MAAMC,SAAS,GAAG,CAAC,IAAIF,cAAc,IAAI,EAAE,CAAC,EAAE,IAAIC,aAAa,IAAI,EAAE,CAAC,CAAC;IAEvE,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1BT,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3CpE,UAAU,EAAE,CAAAyE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,MAAM,KAAI,CAAC;MACvC1E,SAAS,EAAE,CAAAwE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,MAAM,KAAI,CAAC;MACrCC,MAAM,EAAEF,SAAS,CAACC;IACpB,CAAC,CAAC;IAEF,MAAMvC,UAAU,GAAGsC,SAAS,CAACC,MAAM;;IAEnC;IACA,MAAMtC,cAAc,GAAGqC,SAAS,CAACG,MAAM,CAACrD,IAAI,IAC1CA,IAAI,CAACsD,mBAAmB,KAAK,YAAY,IACzCtD,IAAI,CAACsD,mBAAmB,KAAK,YAAY,IACzCtD,IAAI,CAACsD,mBAAmB,KAAK,QAC/B,CAAC,CAACH,MAAM;IAER,MAAMrC,gBAAgB,GAAGoC,SAAS,CAACG,MAAM,CAACrD,IAAI,IAC5CA,IAAI,CAACsD,mBAAmB,KAAK,eAAe,IAC5CtD,IAAI,CAACsD,mBAAmB,KAAK,eAC/B,CAAC,CAACH,MAAM;IAER,MAAMpC,WAAW,GAAGmC,SAAS,CAACG,MAAM,CAACrD,IAAI,IACvCA,IAAI,CAACsD,mBAAmB,KAAK,UAAU,IACvCtD,IAAI,CAACsD,mBAAmB,KAAK,UAC/B,CAAC,CAACH,MAAM;;IAER;IACA,MAAMnC,aAAa,GAAGkC,SAAS,CAACG,MAAM,CAACrD,IAAI,IACzCA,IAAI,CAACuD,YAAY,KAAK,CAAC,IACvBvD,IAAI,CAACwD,qBAAqB,IAC1BxD,IAAI,CAACyD,mBACP,CAAC,CAACN,MAAM;IAER,MAAMlC,gBAAgB,GAAGL,UAAU,GAAGI,aAAa;;IAEnD;IACA,MAAME,eAAe,GAAGgC,SAAS,CAACG,MAAM,CAACrD,IAAI,IAAIA,IAAI,CAAC0D,WAAW,KAAK,IAAI,IAAI1D,IAAI,CAAC0D,WAAW,KAAK,MAAM,CAAC,CAACP,MAAM;;IAEjH;IACA,MAAM5B,GAAG,GAAGM,YAAY,CAACjB,UAAU,EAAEC,cAAc,EAAEG,aAAa,EAAEE,eAAe,CAAC;;IAEpF;IACA,MAAME,wBAAwB,GAAGG,GAAG,CAAC,CAAC;IACtC,MAAMF,uBAAuB,GAAGT,UAAU,GAAG,CAAC,GAAGiC,IAAI,CAACC,KAAK,CAAE9B,aAAa,GAAGJ,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEnG;IACA,MAAMY,WAAW,GAAG0B,SAAS,CAACS,MAAM,CAAC,CAACC,GAAG,EAAE5D,IAAI,KAAK4D,GAAG,IAAIC,UAAU,CAAC7D,IAAI,CAAC8D,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnG,MAAMrC,eAAe,GAAGyB,SAAS,CAC9BG,MAAM,CAACrD,IAAI,IAAIA,IAAI,CAACsD,mBAAmB,KAAK,YAAY,IAAItD,IAAI,CAACsD,mBAAmB,KAAK,YAAY,IAAItD,IAAI,CAACsD,mBAAmB,KAAK,QAAQ,CAAC,CAC/IK,MAAM,CAAC,CAACC,GAAG,EAAE5D,IAAI,KAAK4D,GAAG,IAAIC,UAAU,CAAC7D,IAAI,CAAC+D,eAAe,CAAC,IAAIF,UAAU,CAAC7D,IAAI,CAAC8D,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5G,MAAMpC,cAAc,GAAGF,WAAW,GAAGC,eAAe;IACpD,MAAMN,kBAAkB,GAAGP,UAAU,GAAGM,eAAe;IACvD,MAAMI,yBAAyB,GAAGV,UAAU,GAAG,CAAC,GAAGiC,IAAI,CAACC,KAAK,CAAE5B,eAAe,GAAGN,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IAEvG,MAAMoD,aAAa,GAAG;MACpBpD,UAAU;MACVC,cAAc;MACdC,gBAAgB;MAChBC,WAAW;MACXC,aAAa;MACbC,gBAAgB;MAChBC,eAAe;MACfC,kBAAkB;MAClBC,wBAAwB;MACxBC,uBAAuB;MACvBC,yBAAyB;MACzBC,GAAG;MAAE;MACLC,WAAW,EAAEqB,IAAI,CAACC,KAAK,CAACtB,WAAW,CAAC;MACpCC,eAAe,EAAEoB,IAAI,CAACC,KAAK,CAACrB,eAAe,CAAC;MAC5CC,cAAc,EAAEmB,IAAI,CAACC,KAAK,CAACpB,cAAc;IAC3C,CAAC;IAEDgB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEqB,aAAa,CAAC;IAC1DrD,aAAa,CAACqD,aAAa,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAG,MAAOC,eAAe,IAAK;IACvD,IAAI;MACF,MAAMC,qBAAqB,GAAG,MAAMzH,WAAW,CAAC0H,oBAAoB,CAACF,eAAe,CAAC;MACrF;MACAxC,oBAAoB,CAACyC,qBAAqB,CAACE,kBAAkB,CAAC;IAChE,CAAC,CAAC,OAAO1F,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE;IACF;EACF,CAAC;;EAMD;EACA,MAAM,CAAC2F,OAAO,EAAEC,UAAU,CAAC,GAAGjM,QAAQ,CAAC;IACrC8K,mBAAmB,EAAE,EAAE;IACvBoB,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEX,qBAAqB,CAAC,GAAG1L,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACsM,aAAa,EAAEC,gBAAgB,CAAC,GAAGvM,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA;EACA,MAAMwM,SAAS,GAAG,MAAAA,CAAOC,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI;MACF,IAAI,CAACA,aAAa,EAAE;QAClBrG,UAAU,CAAC,IAAI,CAAC;MAClB;MACA8D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAExE,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACfuE,OAAO,CAAC7D,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAIwF,eAAe,GAAGjG,UAAU;MAChC,IAAI,CAACiG,eAAe,EAAE;QACpBA,eAAe,GAAGc,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5DzC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEyB,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpB1B,OAAO,CAAC7D,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACA8D,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAIyC,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAMxI,WAAW,CAACyI,OAAO,CAACjB,eAAe,EAAE,CAAC,EAAEI,OAAO,CAAC;QAC/D9B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEyC,MAAM,GAAGA,MAAM,CAACjC,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOmC,WAAW,EAAE;QACpB5C,OAAO,CAAC7D,KAAK,CAAC,yCAAyC,EAAEyG,WAAW,CAAC;QACrE;QACAF,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACjC,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAMoC,kBAAkB,GAAGH,MAAM,CAAC/B,MAAM,CAACrD,IAAI,IAAIA,IAAI,CAACwF,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAID,kBAAkB,CAACpC,MAAM,GAAG,CAAC,EAAE;UACjCT,OAAO,CAAC7D,KAAK,CAAC,wEAAwE,EAAE0G,kBAAkB,CAAC;QAC7G;MACF;MAEA/G,aAAa,CAAC4G,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIK,KAAK,GAAG,EAAE;MACd,IAAI;QACF/C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9D8C,KAAK,GAAG,MAAM7I,WAAW,CAAC8I,YAAY,CAACtB,eAAe,CAAC;QACvD1B,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE8C,KAAK,GAAGA,KAAK,CAACtC,MAAM,GAAG,CAAC,CAAC;QACnF,IAAIsC,KAAK,IAAIA,KAAK,CAACtC,MAAM,GAAG,CAAC,EAAE;UAC7BT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8C,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBjD,OAAO,CAAC7D,KAAK,CAAC,8DAA8D,EAAE8G,UAAU,CAAC;QACzF;QACA,IAAI;UACFjD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C8C,KAAK,GAAG,MAAM7I,WAAW,CAACyI,OAAO,CAACjB,eAAe,EAAE,CAAC,CAAC;UACrD1B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE8C,KAAK,GAAGA,KAAK,CAACtC,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOyC,aAAa,EAAE;UACtBlD,OAAO,CAAC7D,KAAK,CAAC,mCAAmC,EAAE+G,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACA/G,YAAY,CAAC+G,KAAK,IAAI,EAAE,CAAC;;MAIzB;MACA3G,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACA2G,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzEvD,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7EuD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACR,IAAI,CAACnB,aAAa,EAAE;QAClBrG,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACAnG,SAAS,CAAC,MAAM;IACd;IACAwL,sBAAsB,CAAC,CAAC;IAExB,MAAMoC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF3D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM2D,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CzC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC2D,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVxH,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAI2H,kBAAkB,GAAGrB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIqB,oBAAoB,GAAGtB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEzC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAE4D,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnG9D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEhF,IAAI,CAAC;;QAEjC;QACA+E,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAI8D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAAC/B,MAAM,EAAEsD,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGxB,YAAY,CAACwB,GAAG,CAACD,CAAC,CAAC;UAC/B/D,OAAO,CAACC,GAAG,CAAC,GAAG+D,GAAG,KAAKxB,YAAY,CAACC,OAAO,CAACuB,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAA/I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgJ,IAAI,MAAK,eAAe,EAAE;UAClCjE,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIhF,IAAI,CAACiJ,WAAW,EAAE;YACpBlE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEhF,IAAI,CAACiJ,WAAW,CAAC;YACrEL,kBAAkB,GAAG5I,IAAI,CAACiJ,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAG7I,IAAI,CAACmJ,aAAa,IAAI,YAAYnJ,IAAI,CAACiJ,WAAW,EAAE;;YAE3E;YACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClE9D,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4D,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACF7D,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAM2D,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAImB,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvC1E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiF,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBlE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEiF,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClE9D,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4D,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACVrF,OAAO,CAAC7D,KAAK,CAAC,6CAA6C,EAAEkJ,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9F7D,OAAO,CAACsF,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAtB,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClE9D,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE4D,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBzH,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMqJ,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtD7D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEsF,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxBnJ,QAAQ,CAAC,2BAA2ByH,kBAAkB,mCAAmC,CAAC;UAC1F3H,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAAC6J,aAAa,CAAC;QAC5B3J,eAAe,CAACkI,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,MAAM9D,qBAAqB,CAAC8D,aAAa,CAAC;;QAM1C;QACAvF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsF,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACA9F,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE6B,OAAO,CAAC;UAC1E,MAAMiE,WAAW,GAAG7L,WAAW,CAACyI,OAAO,CAAC4C,aAAa,EAAE,CAAC,EAAEzD,OAAO,CAAC;UAClE,MAAMY,MAAM,GAAG,MAAMiD,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhE1F,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEyC,MAAM,CAAC;UAC5C1C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEyC,MAAM,GAAGA,MAAM,CAACjC,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIiC,MAAM,IAAIA,MAAM,CAACjC,MAAM,GAAG,CAAC,EAAE;YAC/BT,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyC,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACL1C,OAAO,CAACsF,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAzJ,aAAa,CAAC4G,MAAM,IAAI,EAAE,CAAC;;UAE3B;UACArC,mBAAmB,CAACqC,MAAM,IAAI,EAAE,EAAE3G,SAAS,CAAC;QAC9C,CAAC,CAAC,OAAOkK,SAAS,EAAE;UAClBjG,OAAO,CAAC7D,KAAK,CAAC,yCAAyC,EAAE8J,SAAS,CAAC;UACnEjG,OAAO,CAAC7D,KAAK,CAAC,8BAA8B,EAAE;YAC5CK,OAAO,EAAEyJ,SAAS,CAACzJ,OAAO;YAC1B0J,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACArK,aAAa,CAAC,EAAE,CAAC;UACjBkE,OAAO,CAACsF,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACAlJ,QAAQ,CAAC,2CAA2C6J,SAAS,CAACzJ,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAwD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEsF,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACA9F,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAMwG,YAAY,GAAGvM,WAAW,CAACyI,OAAO,CAAC4C,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMxC,KAAK,GAAG,MAAM4C,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhE1F,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8C,KAAK,CAAC;UAC1C/C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE8C,KAAK,GAAGA,KAAK,CAACtC,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIsC,KAAK,IAAIA,KAAK,CAACtC,MAAM,GAAG,CAAC,EAAE;YAC7BT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8C,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACL/C,OAAO,CAACsF,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACAvJ,YAAY,CAAC+G,KAAK,IAAI,EAAE,CAAC;;UAEzB;UACA1C,mBAAmB,CAACxE,UAAU,EAAEkH,KAAK,IAAI,EAAE,CAAC;QAC9C,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBjD,OAAO,CAAC7D,KAAK,CAAC,wCAAwC,EAAE8G,UAAU,CAAC;UACnEjD,OAAO,CAAC7D,KAAK,CAAC,6BAA6B,EAAE;YAC3CK,OAAO,EAAEyG,UAAU,CAACzG,OAAO;YAC3B0J,MAAM,EAAEjD,UAAU,CAACiD,MAAM;YACzBC,IAAI,EAAElD,UAAU,CAACkD,IAAI;YACrBC,KAAK,EAAEnD,UAAU,CAACmD,KAAK;YACvBC,IAAI,EAAEpD,UAAU,CAACoD,IAAI;YACrBC,IAAI,EAAErD,UAAU,CAACqD,IAAI;YACrBC,QAAQ,EAAEtD,UAAU,CAACsD,QAAQ,GAAG;cAC9BL,MAAM,EAAEjD,UAAU,CAACsD,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEvD,UAAU,CAACsD,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAElD,UAAU,CAACsD,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAnK,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0C6G,UAAU,CAACzG,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACAN,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOwK,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZhH,OAAO,CAAC7D,KAAK,CAAC,kCAAkC,EAAEuK,GAAG,CAAC;QACtD1G,OAAO,CAAC7D,KAAK,CAAC,2BAA2B,EAAE;UACzCK,OAAO,EAAEkK,GAAG,CAAClK,OAAO;UACpB0J,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAClK,OAAO,IAAIkK,GAAG,CAAClK,OAAO,CAAC+G,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE0D,YAAY,GAAGP,GAAG,CAAClK,OAAO;QAC5B,CAAC,MAAM,IAAIkK,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAClK,OAAO,EAAE;UACtByK,YAAY,GAAGP,GAAG,CAAClK,OAAO;QAC5B;QAEAJ,QAAQ,CAAC,gCAAgC6K,YAAY,sBAAsB,CAAC;;QAE5E;QACAnL,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDyH,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMqF,iBAAiB,GAAI7J,IAAI,IAAK;IAClCX,eAAe,CAACW,IAAI,CAAC;IACrBT,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMuK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvK,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM0K,uBAAuB,GAAGA,CAAA,KAAM;IACpC/K,eAAe,CAACgL,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE/K,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED;EACA,MAAMgL,gBAAgB,GAAGA,CAAC/K,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAC1DH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAM+K,qBAAqB,GAAGA,CAAA,KAAM;IAClCzK,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;IACtC;IACA,IAAIA,gBAAgB,EAAE;MACpBG,qBAAqB,CAAC,EAAE,CAAC;MACzBE,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;EAED,MAAMsK,+BAA+B,GAAIC,WAAW,IAAK;IACvDzK,qBAAqB,CAACyK,WAAW,CAAC;EACpC,CAAC;EAED,MAAMC,8BAA8B,GAAID,WAAW,IAAK;IACtDvK,oBAAoB,CAACuK,WAAW,CAAC;EACnC,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,kBAAkB,GAAGhM,UAAU,CAAC8E,MAAM,CAACrD,IAAI,IAAIN,kBAAkB,CAACuG,QAAQ,CAACjG,IAAI,CAACwK,OAAO,CAAC,CAAC;IAC/F,MAAMC,iBAAiB,GAAGhM,SAAS,CAAC4E,MAAM,CAACrD,IAAI,IAAIJ,iBAAiB,CAACqG,QAAQ,CAACjG,IAAI,CAACwK,OAAO,CAAC,CAAC;IAC5F,OAAO,CAAC,GAAGD,kBAAkB,EAAE,GAAGE,iBAAiB,CAAC;EACtD,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAOhL,kBAAkB,CAACyD,MAAM,GAAGvD,iBAAiB,CAACuD,MAAM;EAC7D,CAAC;;EAED;EACA,MAAMwH,uBAAuB,GAAGA,CAAC3K,IAAI,EAAE4K,MAAM,KAAK;IAChDlI,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEiI,MAAM,EAAE,WAAW,EAAE5K,IAAI,CAAC;IAElE,QAAQ4K,MAAM;MACZ,KAAK,cAAc;QACjBf,iBAAiB,CAAC7J,IAAI,CAAC;QACvB;MACF,KAAK,MAAM;QACT;QACAiK,gBAAgB,CAAC,2CAA2C,EAAE,MAAM,CAAC;QACrE;MACF,KAAK,QAAQ;QACX;QACAA,gBAAgB,CAAC,+CAA+C,EAAE,MAAM,CAAC;QACzE;MACF,KAAK,QAAQ;QACX,IAAI1L,UAAU,CAACsM,IAAI,CAACrD,CAAC,IAAIA,CAAC,CAACgD,OAAO,KAAKxK,IAAI,CAACwK,OAAO,CAAC,EAAE;UACpD;UACA,MAAMM,UAAU,GAAGpL,kBAAkB,CAACuG,QAAQ,CAACjG,IAAI,CAACwK,OAAO,CAAC;UAC5D,IAAIM,UAAU,EAAE;YACdnL,qBAAqB,CAACqK,IAAI,IAAIA,IAAI,CAAC3G,MAAM,CAAC0H,EAAE,IAAIA,EAAE,KAAK/K,IAAI,CAACwK,OAAO,CAAC,CAAC;UACvE,CAAC,MAAM;YACL7K,qBAAqB,CAACqK,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEhK,IAAI,CAACwK,OAAO,CAAC,CAAC;UACxD;QACF,CAAC,MAAM;UACL;UACA,MAAMM,UAAU,GAAGlL,iBAAiB,CAACqG,QAAQ,CAACjG,IAAI,CAACwK,OAAO,CAAC;UAC3D,IAAIM,UAAU,EAAE;YACdjL,oBAAoB,CAACmK,IAAI,IAAIA,IAAI,CAAC3G,MAAM,CAAC0H,EAAE,IAAIA,EAAE,KAAK/K,IAAI,CAACwK,OAAO,CAAC,CAAC;UACtE,CAAC,MAAM;YACL3K,oBAAoB,CAACmK,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEhK,IAAI,CAACwK,OAAO,CAAC,CAAC;UACvD;QACF;QACA;QACA,IAAI,CAAChL,gBAAgB,EAAE;UACrBC,mBAAmB,CAAC,IAAI,CAAC;QAC3B;QACA;MACF,KAAK,SAAS;QACZ,MAAMuL,kBAAkB,GAAGN,qBAAqB,CAAC,CAAC;QAClD,IAAIM,kBAAkB,GAAG,CAAC,EAAE;UAC1B;UACA,MAAMC,eAAe,GAAGX,kBAAkB,CAAC,CAAC;UAC5C,MAAMY,MAAM,GAAGD,eAAe,CAAC1D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACgD,OAAO,CAAC,CAAC7C,IAAI,CAAC,IAAI,CAAC;UAC7DwD,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,MAAM,CAAC;UACrCjB,gBAAgB,CAAC,GAAGe,kBAAkB,iCAAiC,EAAE,SAAS,CAAC;QACrF,CAAC,MAAM;UACLG,SAAS,CAACC,SAAS,CAACC,SAAS,CAACrL,IAAI,CAACwK,OAAO,CAAC;UAC3CP,gBAAgB,CAAC,WAAWjK,IAAI,CAACwK,OAAO,wBAAwB,EAAE,SAAS,CAAC;QAC9E;QACA;MACF,KAAK,cAAc;QACjB,MAAMc,aAAa,GAAGZ,qBAAqB,CAAC,CAAC;QAC7C,IAAIY,aAAa,GAAG,CAAC,EAAE;UACrB;UACA,MAAML,eAAe,GAAGX,kBAAkB,CAAC,CAAC;UAC5C,MAAMiB,UAAU,GAAGN,eAAe,CAAC1D,GAAG,CAACC,CAAC,IACtC,OAAOA,CAAC,CAACgD,OAAO,gBAAgBhD,CAAC,CAAC9C,SAAS,cAAc8C,CAAC,CAACgE,OAAO,YAAYhE,CAAC,CAAC1D,aAAa,EAC/F,CAAC,CAAC6D,IAAI,CAAC,MAAM,CAAC;UACdwD,SAAS,CAACC,SAAS,CAACC,SAAS,CAACE,UAAU,CAAC;UACzCtB,gBAAgB,CAAC,eAAeqB,aAAa,6BAA6B,EAAE,SAAS,CAAC;QACxF,CAAC,MAAM;UACL,MAAMG,OAAO,GAAG,OAAOzL,IAAI,CAACwK,OAAO,gBAAgBxK,IAAI,CAAC0E,SAAS,cAAc1E,IAAI,CAACwL,OAAO,YAAYxL,IAAI,CAAC8D,aAAa,EAAE;UAC3HqH,SAAS,CAACC,SAAS,CAACC,SAAS,CAACI,OAAO,CAAC;UACtCxB,gBAAgB,CAAC,qCAAqC,EAAE,SAAS,CAAC;QACpE;QACA;MACF,KAAK,SAAS;QACZ;QACAA,gBAAgB,CAAC,2CAA2C,EAAE,MAAM,CAAC;QACrE;MACF;MACA,KAAK,qBAAqB;QACxByB,2BAA2B,CAAC,MAAM,CAAC;QACnC;MACF,KAAK,sCAAsC;QACzCA,2BAA2B,CAAC,uBAAuB,CAAC;QACpD;MACF,KAAK,oCAAoC;QACvCA,2BAA2B,CAAC,qBAAqB,CAAC;QAClD;MACF,KAAK,+BAA+B;QAClCA,2BAA2B,CAAC,gBAAgB,CAAC;QAC7C;MACF;MACA,KAAK,gBAAgB;QACnBC,kBAAkB,CAAC3L,IAAI,CAAC;QACxB;MACF,KAAK,qBAAqB;QACxB4L,uBAAuB,CAAC5L,IAAI,CAAC;QAC7B;MACF;MACA,KAAK,yBAAyB;QAC5B6L,0BAA0B,CAAC,CAAC;QAC5B;MACF,KAAK,+BAA+B;QAClCC,gCAAgC,CAAC,CAAC;QAClC;MACF;QACEpJ,OAAO,CAACsF,IAAI,CAAC,0BAA0B,EAAE4C,MAAM,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMmB,wBAAwB,GAAIvL,WAAW,IAAK;IAChD;IACA,OAAO;MAAEwL,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMT,2BAA2B,GAAIlL,WAAW,IAAK;IACnD,MAAMyK,eAAe,GAAGX,kBAAkB,CAAC,CAAC;IAE5C,IAAIW,eAAe,CAAC9H,MAAM,KAAK,CAAC,EAAE;MAChC8G,gBAAgB,CAAC,wDAAwD,EAAE,SAAS,CAAC;MACrF;IACF;IAEAvH,OAAO,CAACC,GAAG,CAAC,qBAAqBnC,WAAW,QAAQyK,eAAe,CAAC9H,MAAM,QAAQ,EAAE8H,eAAe,CAAC1D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACgD,OAAO,CAAC,CAAC;;IAExH;IACAjK,sBAAsB,CAAC;MACrBtB,IAAI,EAAE,IAAI;MACVuB,WAAW,EAAEA,WAAW;MACxBC,eAAe,EAAEwK,eAAe;MAChCtM,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyN,wBAAwB,GAAGA,CAAA,KAAM;IACrC7L,sBAAsB,CAAC;MACrBtB,IAAI,EAAE,KAAK;MACXuB,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnB9B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0N,0BAA0B,GAAIpD,QAAQ,IAAK;IAC/CgB,gBAAgB,CAAC,WAAWhB,QAAQ,CAACqD,cAAc,uBAAuB,EAAE,SAAS,CAAC;;IAEtF;IACA3M,qBAAqB,CAAC,EAAE,CAAC;IACzBE,oBAAoB,CAAC,EAAE,CAAC;;IAExB;IACAuM,wBAAwB,CAAC,CAAC;;IAE1B;IACAvG,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;EACxC,CAAC;;EAED;EACA,MAAMuH,wBAAwB,GAAI1N,KAAK,IAAK;IAC1C6D,OAAO,CAAC7D,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC7DoL,gBAAgB,CAAC,sCAAsC,EAAE,OAAO,CAAC;EACnE,CAAC;;EAED;EACA,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjU,QAAQ,CAAC;IAC3DyG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACV0M,OAAO,EAAE,EAAE;IACX/N,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACgO,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGpU,QAAQ,CAAC;IACrEyG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACV6M,eAAe,EAAE,IAAI;IACrBlO,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMgN,kBAAkB,GAAG,MAAO3L,IAAI,IAAK;IACzC0C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE3C,IAAI,CAACwK,OAAO,CAAC;IACxD9H,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAExE,UAAU,CAAC;IAE3C,IAAI;MACFuE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD8J,qBAAqB,CAAC;QACpBxN,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACV0M,OAAO,EAAE,EAAE;QACX/N,OAAO,EAAE;MACX,CAAC,CAAC;;MAEF;MACA+D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMmK,cAAc,GAAG,MAAM,MAAM,CAAC,+BAA+B,CAAC;MACpE,MAAMJ,OAAO,GAAG,MAAMI,cAAc,CAACC,OAAO,CAACC,UAAU,CAAC7O,UAAU,CAAC;MACnEuE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+J,OAAO,CAAC;;MAE5C;MACA,IAAIO,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACT,OAAO,CAAC,EAAE;QAC1BO,YAAY,GAAGP,OAAO;MACxB,CAAC,MAAM,IAAIA,OAAO,IAAIQ,KAAK,CAACC,OAAO,CAACT,OAAO,CAACA,OAAO,CAAC,EAAE;QACpDO,YAAY,GAAGP,OAAO,CAACA,OAAO;MAChC,CAAC,MAAM,IAAIA,OAAO,IAAIQ,KAAK,CAACC,OAAO,CAACT,OAAO,CAAC7D,IAAI,CAAC,EAAE;QACjDoE,YAAY,GAAGP,OAAO,CAAC7D,IAAI;MAC7B;MAEAnG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEsK,YAAY,CAAC;;MAEzD;MACA,MAAMG,kBAAkB,GAAGH,YAAY,CAAC5J,MAAM,CAACgK,OAAO,IAAI;QACxD;QACA,OAAOA,OAAO,CAACC,KAAK,KAAK,YAAY,IAAID,OAAO,CAACC,KAAK,KAAK,YAAY;MACzE,CAAC,CAAC;MAEF5K,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyK,kBAAkB,CAAC;MAEnEX,qBAAqB,CAAC;QACpBxN,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACV0M,OAAO,EAAEU,kBAAkB;QAC3BzO,OAAO,EAAE;MACX,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DoL,gBAAgB,CAAC,kDAAkD,EAAE,OAAO,CAAC;MAC7EwC,qBAAqB,CAAC;QACpBxN,IAAI,EAAE,KAAK;QACXe,IAAI,EAAE,IAAI;QACV0M,OAAO,EAAE,EAAE;QACX/N,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMiN,uBAAuB,GAAI5L,IAAI,IAAK;IACxC0C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE3C,IAAI,CAACwK,OAAO,CAAC;;IAE1D;IACA,MAAMqC,eAAe,GAAG7M,IAAI,CAACuN,YAAY,IAAIvN,IAAI,CAACwN,gBAAgB,IAC3CxN,IAAI,CAACyN,cAAc,IAAIzN,IAAI,CAAC0N,sBAAsB;IAEzE,IAAI,CAACb,eAAe,EAAE;MACpB5C,gBAAgB,CAAC,2CAA2C,EAAE,SAAS,CAAC;MACxE;IACF;IAEA2C,0BAA0B,CAAC;MACzB3N,IAAI,EAAE,IAAI;MACVe,IAAI,EAAEA,IAAI;MACV6M,eAAe,EAAEA,eAAe;MAChClO,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMgP,yBAAyB,GAAG,MAAOC,kBAAkB,IAAK;IAC9D,IAAI;MACFnB,qBAAqB,CAACzC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErL,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MAE3D,MAAMmO,cAAc,GAAG,MAAM,MAAM,CAAC,+BAA+B,CAAC;MAEpE,IAAIN,kBAAkB,CAACqB,UAAU,IAAIrB,kBAAkB,CAACsB,YAAY,EAAE;QACpE;QACA,MAAMC,WAAW,GAAGvB,kBAAkB,CAACsB,YAAY,CAACvG,GAAG,CAACvH,IAAI,IAAIA,IAAI,CAACwK,OAAO,CAAC;QAE7E,MAAMsC,cAAc,CAACC,OAAO,CAACiB,mBAAmB,CAC9CJ,kBAAkB,CAACtB,cAAc,EACjCyB,WACF,CAAC;QAED9D,gBAAgB,CACd,GAAG8D,WAAW,CAAC5K,MAAM,+BAA+ByK,kBAAkB,CAACtB,cAAc,EAAE,EACvF,SACF,CAAC;;QAED;QACA3M,qBAAqB,CAAC,EAAE,CAAC;QACzBE,oBAAoB,CAAC,EAAE,CAAC;MAE1B,CAAC,MAAM;QACL;QACA,MAAMiN,cAAc,CAACC,OAAO,CAACiB,mBAAmB,CAC9CJ,kBAAkB,CAACtB,cAAc,EACjC,CAACE,kBAAkB,CAACxM,IAAI,CAACwK,OAAO,CAClC,CAAC;QAEDP,gBAAgB,CACd,QAAQuC,kBAAkB,CAACxM,IAAI,CAACwK,OAAO,0BAA0BoD,kBAAkB,CAACtB,cAAc,EAAE,EACpG,SACF,CAAC;MACH;;MAEA;MACAG,qBAAqB,CAAC;QACpBxN,IAAI,EAAE,KAAK;QACXe,IAAI,EAAE,IAAI;QACV0M,OAAO,EAAE,EAAE;QACX/N,OAAO,EAAE,KAAK;QACdkP,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAChB,CAAC,CAAC;;MAEF;MACAjI,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAExC,CAAC,CAAC,OAAOnG,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpEoL,gBAAgB,CAAC,6CAA6C,EAAE,OAAO,CAAC;MACxEwC,qBAAqB,CAACzC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErL,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC;;EAED;EACA,MAAMsP,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IACjD,IAAI;MACFrB,0BAA0B,CAAC5C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErL,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MAEhE,MAAMmO,cAAc,GAAG,MAAM,MAAM,CAAC,+BAA+B,CAAC;MAEpE,IAAIH,uBAAuB,CAACkB,UAAU,IAAIlB,uBAAuB,CAACmB,YAAY,EAAE;QAC9E;QACA,IAAII,YAAY,GAAG,CAAC;QACpB,IAAIC,UAAU,GAAG,CAAC;QAElB,KAAK,MAAMnO,IAAI,IAAI2M,uBAAuB,CAACmB,YAAY,EAAE;UACvD,IAAI;YACF,MAAMjB,eAAe,GAAG7M,IAAI,CAACuN,YAAY,IAAIvN,IAAI,CAACwN,gBAAgB,IAC3CxN,IAAI,CAACyN,cAAc,IAAIzN,IAAI,CAAC0N,sBAAsB;YAEzE,IAAIb,eAAe,EAAE;cACnB,MAAMC,cAAc,CAACC,OAAO,CAACqB,oBAAoB,CAACvB,eAAe,EAAE7M,IAAI,CAACwK,OAAO,CAAC;cAChF0D,YAAY,EAAE;YAChB;UACF,CAAC,CAAC,OAAOrP,KAAK,EAAE;YACd6D,OAAO,CAAC7D,KAAK,CAAC,mCAAmCmB,IAAI,CAACwK,OAAO,GAAG,EAAE3L,KAAK,CAAC;YACxEsP,UAAU,EAAE;UACd;QACF;QAEA,IAAID,YAAY,GAAG,CAAC,EAAE;UACpBjE,gBAAgB,CACd,GAAGiE,YAAY,8BAA8BC,UAAU,GAAG,CAAC,GAAG,KAAKA,UAAU,UAAU,GAAG,EAAE,EAAE,EAC9FA,UAAU,GAAG,CAAC,GAAG,SAAS,GAAG,SAC/B,CAAC;QACH,CAAC,MAAM;UACLlE,gBAAgB,CAAC,+CAA+C,EAAE,OAAO,CAAC;QAC5E;;QAEA;QACAtK,qBAAqB,CAAC,EAAE,CAAC;QACzBE,oBAAoB,CAAC,EAAE,CAAC;MAE1B,CAAC,MAAM;QACL;QACA,MAAMiN,cAAc,CAACC,OAAO,CAACqB,oBAAoB,CAC/CzB,uBAAuB,CAACE,eAAe,EACvCF,uBAAuB,CAAC3M,IAAI,CAACwK,OAC/B,CAAC;QAEDP,gBAAgB,CACd,QAAQ0C,uBAAuB,CAAC3M,IAAI,CAACwK,OAAO,0BAA0BmC,uBAAuB,CAACE,eAAe,EAAE,EAC/G,SACF,CAAC;MACH;;MAEA;MACAD,0BAA0B,CAAC;QACzB3N,IAAI,EAAE,KAAK;QACXe,IAAI,EAAE,IAAI;QACV6M,eAAe,EAAE,IAAI;QACrBlO,OAAO,EAAE,KAAK;QACdkP,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAChB,CAAC,CAAC;;MAEF;MACAjI,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAExC,CAAC,CAAC,OAAOnG,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtEoL,gBAAgB,CAAC,+CAA+C,EAAE,OAAO,CAAC;MAC1E2C,0BAA0B,CAAC5C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErL,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAMkN,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,MAAMZ,eAAe,GAAGX,kBAAkB,CAAC,CAAC;IAE5C,IAAIW,eAAe,CAAC9H,MAAM,KAAK,CAAC,EAAE;MAChC8G,gBAAgB,CAAC,yBAAyB,EAAE,SAAS,CAAC;MACtD;IACF;IAEAvH,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEsI,eAAe,CAAC1D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACgD,OAAO,CAAC,CAAC;IAExF,IAAI;MACF9H,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D8J,qBAAqB,CAAC;QACpBxN,IAAI,EAAE,IAAI;QACVe,IAAI,EAAE;UAAEwK,OAAO,EAAE,GAAGS,eAAe,CAAC9H,MAAM;QAAoB,CAAC;QAAE;QACjEuJ,OAAO,EAAE,EAAE;QACX/N,OAAO,EAAE,IAAI;QACbkP,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE7C;MAChB,CAAC,CAAC;;MAEF;MACAvI,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMmK,cAAc,GAAG,MAAM,MAAM,CAAC,+BAA+B,CAAC;MACpE,MAAMJ,OAAO,GAAG,MAAMI,cAAc,CAACC,OAAO,CAACC,UAAU,CAAC7O,UAAU,CAAC;MACnEuE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+J,OAAO,CAAC;;MAE5C;MACA,IAAIO,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACT,OAAO,CAAC,EAAE;QAC1BO,YAAY,GAAGP,OAAO;MACxB,CAAC,MAAM,IAAIA,OAAO,IAAIQ,KAAK,CAACC,OAAO,CAACT,OAAO,CAACA,OAAO,CAAC,EAAE;QACpDO,YAAY,GAAGP,OAAO,CAACA,OAAO;MAChC,CAAC,MAAM,IAAIA,OAAO,IAAIQ,KAAK,CAACC,OAAO,CAACT,OAAO,CAAC7D,IAAI,CAAC,EAAE;QACjDoE,YAAY,GAAGP,OAAO,CAAC7D,IAAI;MAC7B;;MAEA;MACA,MAAMuE,kBAAkB,GAAGH,YAAY,CAAC5J,MAAM,CAACgK,OAAO,IAAI;QACxD,OAAOA,OAAO,CAACC,KAAK,KAAK,YAAY,IAAID,OAAO,CAACC,KAAK,KAAK,YAAY;MACzE,CAAC,CAAC;MAEFb,qBAAqB,CAAC;QACpBxN,IAAI,EAAE,IAAI;QACVe,IAAI,EAAE;UAAEwK,OAAO,EAAE,GAAGS,eAAe,CAAC9H,MAAM;QAAoB,CAAC;QAC/DuJ,OAAO,EAAEU,kBAAkB;QAC3BzO,OAAO,EAAE,KAAK;QACdkP,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE7C;MAChB,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOpM,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DoL,gBAAgB,CAAC,kDAAkD,EAAE,OAAO,CAAC;MAC7EwC,qBAAqB,CAAC;QACpBxN,IAAI,EAAE,KAAK;QACXe,IAAI,EAAE,IAAI;QACV0M,OAAO,EAAE,EAAE;QACX/N,OAAO,EAAE,KAAK;QACdkP,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMhC,gCAAgC,GAAGA,CAAA,KAAM;IAC7C,MAAMb,eAAe,GAAGX,kBAAkB,CAAC,CAAC;IAE5C,IAAIW,eAAe,CAAC9H,MAAM,KAAK,CAAC,EAAE;MAChC8G,gBAAgB,CAAC,yBAAyB,EAAE,SAAS,CAAC;MACtD;IACF;;IAEA;IACA,MAAMoE,cAAc,GAAGpD,eAAe,CAAC5H,MAAM,CAACrD,IAAI,IAChDA,IAAI,CAACuN,YAAY,IAAIvN,IAAI,CAACwN,gBAAgB,IAAIxN,IAAI,CAACyN,cAAc,IAAIzN,IAAI,CAAC0N,sBAC5E,CAAC;IAED,IAAIW,cAAc,CAAClL,MAAM,KAAK,CAAC,EAAE;MAC/B8G,gBAAgB,CAAC,wDAAwD,EAAE,MAAM,CAAC;MAClF;IACF;IAEAvH,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE0L,cAAc,CAAC9G,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACgD,OAAO,CAAC,CAAC;IAEzFoC,0BAA0B,CAAC;MACzB3N,IAAI,EAAE,IAAI;MACVe,IAAI,EAAE;QAAEwK,OAAO,EAAE,GAAG6D,cAAc,CAAClL,MAAM;MAAoB,CAAC;MAC9D0J,eAAe,EAAE,UAAU;MAC3BlO,OAAO,EAAE,KAAK;MACdkP,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAEO;IAChB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAItO,IAAI,IAAK;IACpC,MAAM8K,UAAU,GAAGvM,UAAU,CAACsM,IAAI,CAACrD,CAAC,IAAIA,CAAC,CAACgD,OAAO,MAAKxK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwK,OAAO,EAAC,GAChE9K,kBAAkB,CAACuG,QAAQ,CAACjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwK,OAAO,CAAC,GAC1C5K,iBAAiB,CAACqG,QAAQ,CAACjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwK,OAAO,CAAC;IAE7C,MAAMQ,kBAAkB,GAAGN,qBAAqB,CAAC,CAAC;IAClD,MAAM6D,oBAAoB,GAAGvD,kBAAkB,GAAG,CAAC;;IAEnD;IACA,MAAMwD,aAAa,GAAG,CACpB;MACEC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAEH,oBAAoB,GAAG,GAAGvD,kBAAkB,mBAAmB,GAAG,QAAQ,CAAAhL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwK,OAAO,KAAI,EAAE;IACtG,CAAC;IACD;IACA,IAAI+D,oBAAoB,GAAG,CACzB;MACEE,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6B1D,kBAAkB;IACxD,CAAC,EACD;MACED,EAAE,EAAE,qBAAqB;MACzB2D,KAAK,EAAE,cAAc;MACrBC,IAAI,eAAEpR,OAAA,CAACnB,SAAS;QAACwS,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpCpE,MAAM,EAAE,qBAAqB;MAC7BqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,UAAU,GAAGpD,wBAAwB,CAAC,MAAM,CAAC;QACnD,OAAOoD,UAAU,CAACjD,MAAM,GAAG,CAAC,GAAG,OAAO,GAAGiD,UAAU,CAAChD,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MAC1F,CAAC,EAAE,CAAC;MACJiD,WAAW,EAAE,CAAC,MAAM;QAClB,MAAMD,UAAU,GAAGpD,wBAAwB,CAAC,MAAM,CAAC;QACnD,IAAIsD,IAAI,GAAG,yBAAyBrE,kBAAkB,OAAO;QAC7D,IAAImE,UAAU,CAAClD,MAAM,GAAG,CAAC,EAAE;UACzBoD,IAAI,IAAI,KAAKF,UAAU,CAACjD,MAAM,YAAYiD,UAAU,CAAChD,QAAQ,UAAU;QACzE;QACA,OAAOkD,IAAI;MACb,CAAC,EAAE;IACL,CAAC,EACD;MACEtE,EAAE,EAAE,sCAAsC;MAC1C2D,KAAK,EAAE,+BAA+B;MACtCC,IAAI,eAAEpR,OAAA,CAAC7C,QAAQ;QAACkU,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCpE,MAAM,EAAE,sCAAsC;MAC9CqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,UAAU,GAAGpD,wBAAwB,CAAC,uBAAuB,CAAC;QACpE,OAAOoD,UAAU,CAACjD,MAAM,GAAG,CAAC,GAAG,OAAO,GAAGiD,UAAU,CAAChD,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MAC1F,CAAC,EAAE,CAAC;MACJiD,WAAW,EAAE,CAAC,MAAM;QAClB,MAAMD,UAAU,GAAGpD,wBAAwB,CAAC,uBAAuB,CAAC;QACpE,IAAIsD,IAAI,GAAG,0CAA0CrE,kBAAkB,OAAO;QAC9E,IAAImE,UAAU,CAAClD,MAAM,GAAG,CAAC,EAAE;UACzBoD,IAAI,IAAI,KAAKF,UAAU,CAACjD,MAAM,YAAYiD,UAAU,CAAChD,QAAQ,UAAU;QACzE;QACA,OAAOkD,IAAI;MACb,CAAC,EAAE;IACL,CAAC,EACD;MACEtE,EAAE,EAAE,oCAAoC;MACxC2D,KAAK,EAAE,6BAA6B;MACpCC,IAAI,eAAEpR,OAAA,CAAC7C,QAAQ;QAACkU,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCpE,MAAM,EAAE,oCAAoC;MAC5CqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,UAAU,GAAGpD,wBAAwB,CAAC,qBAAqB,CAAC;QAClE,OAAOoD,UAAU,CAACjD,MAAM,GAAG,CAAC,GAAG,OAAO,GAAGiD,UAAU,CAAChD,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MAC1F,CAAC,EAAE,CAAC;MACJiD,WAAW,EAAE,CAAC,MAAM;QAClB,MAAMD,UAAU,GAAGpD,wBAAwB,CAAC,qBAAqB,CAAC;QAClE,IAAIsD,IAAI,GAAG,wCAAwCrE,kBAAkB,OAAO;QAC5E,IAAImE,UAAU,CAAClD,MAAM,GAAG,CAAC,EAAE;UACzBoD,IAAI,IAAI,KAAKF,UAAU,CAACjD,MAAM,YAAYiD,UAAU,CAAChD,QAAQ,UAAU;QACzE;QACA,OAAOkD,IAAI;MACb,CAAC,EAAE;IACL,CAAC,EACD;MACEtE,EAAE,EAAE,+BAA+B;MACnC2D,KAAK,EAAE,wBAAwB;MAC/BC,IAAI,eAAEpR,OAAA,CAACrB,YAAY;QAAC0S,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvCpE,MAAM,EAAE,+BAA+B;MACvCqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,UAAU,GAAGpD,wBAAwB,CAAC,gBAAgB,CAAC;QAC7D,OAAOoD,UAAU,CAACjD,MAAM,GAAG,CAAC,GAAG,OAAO,GAAGiD,UAAU,CAAChD,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MAC1F,CAAC,EAAE,CAAC;MACJiD,WAAW,EAAE,CAAC,MAAM;QAClB,MAAMD,UAAU,GAAGpD,wBAAwB,CAAC,gBAAgB,CAAC;QAC7D,IAAIsD,IAAI,GAAG,mCAAmCrE,kBAAkB,OAAO;QACvE,IAAImE,UAAU,CAAClD,MAAM,GAAG,CAAC,EAAE;UACzBoD,IAAI,IAAI,KAAKF,UAAU,CAACjD,MAAM,YAAYiD,UAAU,CAAChD,QAAQ,UAAU;QACzE;QACA,OAAOkD,IAAI;MACb,CAAC,EAAE;IACL,CAAC,EACD;MACEZ,IAAI,EAAE;IACR,CAAC;IACD;IACA;MACEA,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kCAAkC1D,kBAAkB;IAC7D,CAAC,EACD;MACED,EAAE,EAAE,yBAAyB;MAC7B2D,KAAK,EAAE,0BAA0B;MACjCC,IAAI,eAAEpR,OAAA,CAACjB,cAAc;QAACsS,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzCpE,MAAM,EAAE,yBAAyB;MACjCqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE,SAAS;MAChBE,WAAW,EAAE,oBAAoBpE,kBAAkB;IACrD,CAAC,EACD;MACED,EAAE,EAAE,+BAA+B;MACnC2D,KAAK,EAAE,6BAA6B;MACpCC,IAAI,eAAEpR,OAAA,CAACf,UAAU;QAACoS,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrCpE,MAAM,EAAE,+BAA+B;MACvCqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE,SAAS;MAChBE,WAAW,EAAE,mBAAmBpE,kBAAkB;IACpD,CAAC,EACD;MACEyD,IAAI,EAAE;IACR,CAAC,CACF,GAAG,EAAE,CAAC;IACP;IACA,IAAI,CAACF,oBAAoB,GAAG,CAC1B;MACExD,EAAE,EAAE,cAAc;MAClB2D,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAEpR,OAAA,CAACnC,cAAc;QAACwT,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzCpE,MAAM,EAAE,cAAc;MACtBqE,OAAO,EAAEtE;IACX,CAAC,EACD;MACE8D,IAAI,EAAE;IACR,CAAC;IACD;IACA;MACEA,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;IACT,CAAC,EACD;MACE3D,EAAE,EAAE,gBAAgB;MACpB2D,KAAK,EAAE,oBAAoB;MAC3BC,IAAI,eAAEpR,OAAA,CAACjB,cAAc;QAACsS,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzCpE,MAAM,EAAE,gBAAgB;MACxBqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE,SAAS;MAChBE,WAAW,EAAE;IACf,CAAC,EACD,IAAIpP,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuN,YAAY,IAAIvN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwN,gBAAgB,IAAIxN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyN,cAAc,IAAIzN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0N,sBAAsB,GAAG,CACzG;MACE3C,EAAE,EAAE,qBAAqB;MACzB2D,KAAK,EAAE,oBAAoB;MAC3BC,IAAI,eAAEpR,OAAA,CAACf,UAAU;QAACoS,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrCpE,MAAM,EAAE,qBAAqB;MAC7BqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE,SAAS;MAChBE,WAAW,EAAE,sBAAsB,CAAApP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuN,YAAY,MAAIvN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwN,gBAAgB,MAAIxN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyN,cAAc,MAAIzN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0N,sBAAsB;IACzI,CAAC,CACF,GAAG,EAAE,CAAC,EACP;MACEe,IAAI,EAAE;IACR,CAAC,EACD;MACE1D,EAAE,EAAE,MAAM;MACV2D,KAAK,EAAE,UAAU;MACjBC,IAAI,eAAEpR,OAAA,CAACjC,QAAQ;QAACsT,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCpE,MAAM,EAAE,MAAM;MACdqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE;IACT,CAAC,EACD;MACEnE,EAAE,EAAE,QAAQ;MACZ2D,KAAK,EAAE,SAAS;MAChBC,IAAI,eAAEpR,OAAA,CAAC/B,UAAU;QAACoT,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrCpE,MAAM,EAAE,QAAQ;MAChBqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE;IACT,CAAC,EACD;MACET,IAAI,EAAE;IACR,CAAC,EACD;MACE1D,EAAE,EAAE,SAAS;MACb2D,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAEpR,OAAA,CAAC7B,OAAO;QAACkT,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClCpE,MAAM,EAAE,SAAS;MACjBqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAE;IACT,CAAC,EACD;MACET,IAAI,EAAE;IACR,CAAC,CACF,GAAG,EAAE,CAAC;IACP;IACA;MACE1D,EAAE,EAAE,QAAQ;MACZ2D,KAAK,EAAE5D,UAAU,GAAG,aAAa,GAAG,WAAW;MAC/C6D,IAAI,eAAEpR,OAAA,CAAC3B,aAAa;QAACgT,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxCpE,MAAM,EAAE,QAAQ;MAChBqE,OAAO,EAAEtE,uBAAuB;MAChCuE,KAAK,EAAEpE,UAAU,GAAG,SAAS,GAAG;IAClC,CAAC;IACD;IACA;MACE2D,IAAI,EAAE;IACR,CAAC,EACD;MACE1D,EAAE,EAAE,SAAS;MACb2D,KAAK,EAAEH,oBAAoB,GAAG,uBAAuB,GAAG,UAAU;MAClEI,IAAI,eAAEpR,OAAA,CAACzB,QAAQ;QAAC8S,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCpE,MAAM,EAAE,SAAS;MACjBqE,OAAO,EAAEtE,uBAAuB;MAChC2E,QAAQ,EAAE;IACZ,CAAC,EACD;MACEvE,EAAE,EAAE,cAAc;MAClB2D,KAAK,EAAEH,oBAAoB,GAAG,4BAA4B,GAAG,gBAAgB;MAC7EI,IAAI,eAAEpR,OAAA,CAACzB,QAAQ;QAAC8S,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCpE,MAAM,EAAE,cAAc;MACtBqE,OAAO,EAAEtE,uBAAuB;MAChCyE,WAAW,EAAEb,oBAAoB,GAAG,4CAA4C,GAAG;IACrF,CAAC,CACF;IAED,OAAOC,aAAa;EACtB,CAAC;;EAED;EACA,MAAMe,kBAAkB,GAAG,MAAAA,CAAOvP,IAAI,EAAEwP,UAAU,EAAEC,WAAW,EAAEC,WAAW,KAAK;IAC/EhN,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6M,UAAU,EAAE,WAAW,EAAExP,IAAI,CAAC;IACpE0C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8M,WAAW,CAAC;IACzC/M,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+M,WAAW,CAAC;IAE3C,IAAIF,UAAU,KAAK,eAAe,EAAE;MAClC;MACAzP,uBAAuB,CAAC;QACtBd,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACVrB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI6Q,UAAU,KAAK,aAAa,EAAE;MACvC;MACA9M,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE3C,IAAI,CAACwK,OAAO,CAAC;MACtEpK,uBAAuB,CAAC;QACtBnB,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACVrB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI6Q,UAAU,KAAK,eAAe,IAAIA,UAAU,KAAK,iBAAiB,IAClEA,UAAU,KAAK,mBAAmB,IAAIA,UAAU,KAAK,kBAAkB,IACvEA,UAAU,KAAK,oBAAoB,EAAE;MAE9C;MACA,IAAIxP,IAAI,CAACsD,mBAAmB,KAAK,YAAY,EAAE;QAC7CZ,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE3C,IAAI,CAACwK,OAAO,CAAC;QAClFP,gBAAgB,CAAC,0DAA0D,EAAE,MAAM,CAAC;QACpF;MACF;;MAEA;MACAvH,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE3C,IAAI,CAACwK,OAAO,EAAE,SAAS,EAAEgF,UAAU,CAAC;;MAEpG;MACA3J,UAAU,CAAC,MAAM;QACf3F,qBAAqB,CAAC;UACpBjB,IAAI,EAAE,IAAI;UACVe,IAAI,EAAEA,IAAI;UACVrB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,IAAI6Q,UAAU,KAAK,oBAAoB,EAAE;MAC9C;MACA9M,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE3C,IAAI,CAACwK,OAAO,CAAC;;MAE/E;MACA,IAAInK,iBAAiB,CAACsP,OAAO,EAAE;QAC7BtP,iBAAiB,CAACsP,OAAO,CAACC,0BAA0B,CAAC5P,IAAI,CAAC;MAC5D;IACF,CAAC,MAAM,IAAIwP,UAAU,KAAK,kBAAkB,EAAE;MAC5C;MACA9M,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE3C,IAAI,CAACwK,OAAO,CAAC;;MAErF;MACA,IAAInK,iBAAiB,CAACsP,OAAO,EAAE;QAC7BtP,iBAAiB,CAACsP,OAAO,CAACE,wBAAwB,CAAC7P,IAAI,CAAC;MAC1D;IACF,CAAC,MAAM,IAAIwP,UAAU,KAAK,cAAc,EAAE;MACxC;MACA9M,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE3C,IAAI,CAACwK,OAAO,CAAC;;MAElE;MACA,IAAInK,iBAAiB,CAACsP,OAAO,EAAE;QAC7BtP,iBAAiB,CAACsP,OAAO,CAACG,kBAAkB,CAAC9P,IAAI,CAAC;MACpD;IACF,CAAC,MAAM,IAAIwP,UAAU,KAAK,cAAc,EAAE;MACxC;MACA9M,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE+M,WAAW,CAAC;MACxDhN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAExE,UAAU,CAAC;MAE3C,IAAIuR,WAAW,IAAIvR,UAAU,EAAE;QAC7B;QACA,MAAM4R,UAAU,GAAG,mCAAmCL,WAAW,EAAE;QACnEhN,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoN,UAAU,CAAC;;QAEjD;QACA7J,MAAM,CAACC,QAAQ,CAAC6J,IAAI,GAAGD,UAAU;QAEjC9F,gBAAgB,CAAC,oBAAoByF,WAAW,KAAK,EAAE,MAAM,CAAC;MAChE,CAAC,MAAM;QACLhN,OAAO,CAAC7D,KAAK,CAAC,2DAA2D,EAAE;UACzE6Q,WAAW;UACXvR;QACF,CAAC,CAAC;QACF8L,gBAAgB,CAAC,uCAAuC,EAAE,OAAO,CAAC;MACpE;IACF;EACF,CAAC;;EAKD;EACA,MAAMgG,2BAA2B,GAAI/Q,OAAO,IAAK;IAC/C+K,gBAAgB,CAAC/K,OAAO,EAAE,SAAS,CAAC;IACpC;IACA2G,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;EACxC,CAAC;EAED,MAAMkL,yBAAyB,GAAIhR,OAAO,IAAK;IAC7C+K,gBAAgB,CAAC/K,OAAO,EAAE,OAAO,CAAC;EACpC,CAAC;;EAED;EACA,MAAMiR,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAACrQ,oBAAoB,CAACnB,OAAO,EAAE;MACjCoB,uBAAuB,CAAC;QAAEd,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAMyR,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAACjQ,oBAAoB,CAACxB,OAAO,EAAE;MACjCyB,uBAAuB,CAAC;QAAEnB,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAM0R,uBAAuB,GAAG3X,WAAW,CAAC,MAAM;IAChD,IAAI,CAACuH,kBAAkB,CAACtB,OAAO,EAAE;MAC/BuB,qBAAqB,CAAC8J,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE/K,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAACgB,kBAAkB,CAACtB,OAAO,CAAC,CAAC;;EAMhC;;EAEA;EACA,MAAM2R,eAAe,GAAGA,CAAA,kBACtB/S,OAAA,CAACzE,KAAK;IAACyX,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7CpT,OAAA,CAACtD,KAAK;MAAC2W,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnGpT,OAAA,CAACtD,KAAK;QAAC2W,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDpT,OAAA,CAACnD,SAAS;UAAC8U,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CzR,OAAA,CAAC3E,GAAG;UAAA+X,QAAA,gBACFpT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DjQ,UAAU,CAACE;UAAU;YAAAiO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbzR,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,SAAS;YAAC/B,KAAK,EAAC,gBAAgB;YAAAyB,QAAA,EAAC;UAErD;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERzR,OAAA,CAACtD,KAAK;QAAC2W,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDpT,OAAA,CAACjD,eAAe;UAAC4U,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDzR,OAAA,CAAC3E,GAAG;UAAA+X,QAAA,gBACFpT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DjQ,UAAU,CAACG;UAAc;YAAAgO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACbzR,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,SAAS;YAAC/B,KAAK,EAAC,gBAAgB;YAAAyB,QAAA,EAAC;UAErD;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERzR,OAAA,CAACtD,KAAK;QAAC2W,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDpT,OAAA,CAAC7C,QAAQ;UAACwU,KAAK,EAAC,MAAM;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CzR,OAAA,CAAC3E,GAAG;UAAA+X,QAAA,gBACFpT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DjQ,UAAU,CAACM;UAAa;YAAA6N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACbzR,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,SAAS;YAAC/B,KAAK,EAAC,gBAAgB;YAAAyB,QAAA,EAAC;UAErD;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERzR,OAAA,CAACtD,KAAK;QAAC2W,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDpT,OAAA,CAACrB,YAAY;UAACgT,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDzR,OAAA,CAAC3E,GAAG;UAAA+X,QAAA,gBACFpT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DjQ,UAAU,CAACQ;UAAe;YAAA2N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACbzR,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,SAAS;YAAC/B,KAAK,EAAC,gBAAgB;YAAAyB,QAAA,EAAC;UAErD;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERzR,OAAA,CAACtD,KAAK;QAAC2W,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDpT,OAAA,CAAC3E,GAAG;UAAC2X,EAAE,EAAE;YACPa,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBZ,OAAO,EAAEhQ,UAAU,CAACa,GAAG,IAAI,EAAE,GAAG,cAAc,GACrCb,UAAU,CAACa,GAAG,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAC7DgQ,OAAO,EAAE,MAAM;YACfT,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACApT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAAChC,KAAK,EAAC,OAAO;YAAAyB,QAAA,GAC1DjQ,UAAU,CAACa,GAAG,EAAC,GAClB;UAAA;YAAAsN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNzR,OAAA,CAAC3E,GAAG;UAAA+X,QAAA,gBACFpT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEvE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzR,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,SAAS;YAAC/B,KAAK,EAAC,gBAAgB;YAAAyB,QAAA,GACjDjQ,UAAU,CAACe,eAAe,EAAC,cAC9B;UAAA;YAAAoN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGPrN,iBAAiB,iBAChBpE,OAAA,CAACtD,KAAK;QAAC2W,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,eACpDpT,OAAA,CAAC3E,GAAG;UAAA+X,QAAA,gBACFpT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EACjEhP;UAAiB;YAAAkN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACbzR,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,SAAS;YAAC/B,KAAK,EAAC,gBAAgB;YAAAyB,QAAA,EAAC;UAErD;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;;EAEA;;EAEA;EACA,MAAMwC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACpS,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACE7B,OAAA,CAAC/D,MAAM;MAACyF,IAAI,EAAEK,iBAAkB;MAACmS,OAAO,EAAE3H,kBAAmB;MAAC4H,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAhB,QAAA,gBACnFpT,OAAA,CAAC9D,WAAW;QAAAkX,QAAA,GAAC,iBACI,EAACvR,YAAY,CAACoL,OAAO;MAAA;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdzR,OAAA,CAAC7D,aAAa;QAACkY,QAAQ;QAAAjB,QAAA,eACrBpT,OAAA,CAACvE,IAAI;UAAC6Y,SAAS;UAAChB,OAAO,EAAE,CAAE;UAAAF,QAAA,gBACzBpT,OAAA,CAACvE,IAAI;YAAC8Y,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvBpT,OAAA,CAAC1E,UAAU;cAACoY,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAqB;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/EzR,OAAA,CAAC3E,GAAG;cAAC2X,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBpT,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAAC8S,OAAO,IAAI,KAAK;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAAC+S,OAAO,IAAI,KAAK;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAU;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACsF,SAAS,IAAI,KAAK;cAAA;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAO;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACgT,WAAW,IAAI,KAAK;cAAA;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAErGzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAW;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACoM,OAAO,IAAI,KAAK;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElG,CAAC,eAENzR,OAAA,CAAC1E,UAAU;cAACoY,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAQ;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClEzR,OAAA,CAAC3E,GAAG;cAAC2X,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBpT,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAW;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACiT,mBAAmB,IAAI,KAAK;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAO;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACkT,eAAe,IAAI,KAAK;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAY;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACmT,2BAA2B,IAAI,KAAK;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAa;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACoE,qBAAqB,IAAI,KAAK;cAAA;gBAAAqL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACoO,gBAAgB,IAAI,KAAK;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPzR,OAAA,CAACvE,IAAI;YAAC8Y,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvBpT,OAAA,CAAC1E,UAAU;cAACoY,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAM;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChEzR,OAAA,CAAC3E,GAAG;cAAC2X,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBpT,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAW;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACoT,iBAAiB,IAAI,KAAK;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAO;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACqT,aAAa,IAAI,KAAK;cAAA;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAY;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACsT,yBAAyB,IAAI,KAAK;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAa;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACqE,mBAAmB,IAAI,KAAK;cAAA;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACqO,cAAc,IAAI,KAAK;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENzR,OAAA,CAAC1E,UAAU;cAACoY,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAa;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvEzR,OAAA,CAAC3E,GAAG;cAAC2X,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBpT,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAc;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAAC0E,aAAa,IAAI,KAAK;cAAA;gBAAA+K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAgB;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAAC2E,eAAe,IAAI,GAAG;cAAA;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAM;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjS,2BAA2B,CAACqC,YAAY,CAACkE,mBAAmB,CAAC;cAAA;gBAAAuL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAa;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACmE,YAAY,IAAI,GAAG;cAAA;gBAAAsL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAO;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACuT,SAAS,IAAI,KAAK;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAkB;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACwT,iBAAiB,IAAI,KAAK;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAa;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5P,YAAY,CAACmO,YAAY,IAAI,KAAK;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GzR,OAAA,CAAC1E,UAAU;gBAACoY,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACpT,OAAA;kBAAAoT,QAAA,EAAQ;gBAAqB;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI6D,IAAI,CAACzT,YAAY,CAAC0T,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBzR,OAAA,CAAC5D,aAAa;QAAAgX,QAAA,eACZpT,OAAA,CAACxE,MAAM;UAACkW,OAAO,EAAEnF,kBAAmB;UAAA6G,QAAA,EAAC;QAAM;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAIA,oBACEzR,OAAA,CAAC3E,GAAG;IAACoa,SAAS,EAAC,WAAW;IAAArC,QAAA,EACvBhS,OAAO,gBACNpB,OAAA,CAAC3E,GAAG;MAAC2X,EAAE,EAAE;QAAEgB,OAAO,EAAE,MAAM;QAAE0B,aAAa,EAAE,QAAQ;QAAEnC,UAAU,EAAE,QAAQ;QAAEoC,EAAE,EAAE;MAAE,CAAE;MAAAvC,QAAA,gBACjFpT,OAAA,CAACjE,gBAAgB;QAAC6Z,IAAI,EAAE;MAAG;QAAAtE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BzR,OAAA,CAAC1E,UAAU;QAAC0X,EAAE,EAAE;UAAE2C,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,EAAC;MAAmB;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DzR,OAAA,CAACxE,MAAM;QACLkY,OAAO,EAAC,UAAU;QAClB/B,KAAK,EAAC,SAAS;QACfD,OAAO,EAAEA,CAAA,KAAM/I,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCmK,EAAE,EAAE;UAAE2C,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,EACf;MAED;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJnQ,KAAK,gBACPtB,OAAA,CAAC3E,GAAG;MAAA+X,QAAA,gBACFpT,OAAA,CAACpE,KAAK;QAACgG,QAAQ,EAAC,OAAO;QAACoR,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,GACnC9R,KAAK,EACLA,KAAK,CAACoH,QAAQ,CAAC,eAAe,CAAC,iBAC9B1I,OAAA,CAAC1E,UAAU;UAACoY,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,gBACxCpT,OAAA;YAAAoT,QAAA,EAAQ;UAAa;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAzR,OAAA;YAAAsR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAzR,OAAA;YAAAoT,QAAA,EAAM;UAAa;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRzR,OAAA,CAAC3E,GAAG;QAAC2X,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAE6B,GAAG,EAAE;QAAE,CAAE;QAAAzC,QAAA,eACnCpT,OAAA,CAACxE,MAAM;UACLkY,OAAO,EAAC,WAAW;UACnB+B,SAAS,EAAC,gBAAgB;UAC1B/D,OAAO,EAAEA,CAAA,KAAM/I,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAuK,QAAA,EACzC;QAED;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENzR,OAAA,CAAC3E,GAAG;MAAA+X,QAAA,GAIDL,eAAe,CAAC,CAAC,eAGlB/S,OAAA,CAAC3E,GAAG;QAAC2X,EAAE,EAAE;UAAE2C,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,GAEhBnR,gBAAgB,iBACfjC,OAAA,CAACpE,KAAK;UACJgG,QAAQ,EAAC,MAAM;UACfoR,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UACd7F,MAAM,eACJrN,OAAA,CAACxE,MAAM;YACLmW,KAAK,EAAC,SAAS;YACfiE,IAAI,EAAC,OAAO;YACZlE,OAAO,EAAEA,CAAA,KAAMxP,mBAAmB,CAAC,KAAK,CAAE;YAAAkR,QAAA,EAC3C;UAED;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAA2B,QAAA,eAEDpT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,OAAO;YAAAN,QAAA,gBACzBpT,OAAA;cAAAoT,QAAA,EAAQ;YAAyB;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oDAC/B,eAAAzR,OAAA;cAAAoT,QAAA,EAAQ;YAAY;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,iCACxC,EAACtE,qBAAqB,CAAC,CAAC,GAAG,CAAC,iBAC1BnN,OAAA;cAAAoT,QAAA,GAAM,GAAC,eAAApT,OAAA;gBAAAoT,QAAA,GAASjG,qBAAqB,CAAC,CAAC,EAAC,mBAAiB;cAAA;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR,EAGAxP,gBAAgB,IAAIkL,qBAAqB,CAAC,CAAC,GAAG,CAAC,iBAC9CnN,OAAA,CAAC3E,GAAG;UAAC2X,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACjBpT,OAAA,CAAClE,IAAI;YACHqV,KAAK,EAAE,GAAGhE,qBAAqB,CAAC,CAAC,mBAAoB;YACrDwE,KAAK,EAAC,SAAS;YACf+B,OAAO,EAAC,QAAQ;YAChBkC,IAAI,EAAC;UAAO;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGAqE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIhV,UAAU,CAAC4E,MAAM,GAAG,CAAC,iBAC9D5F,OAAA,CAAC3E,GAAG;UAAC2X,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,SAAS;YAAEY,YAAY,EAAE,CAAC;YAAE1C,QAAQ,EAAE,QAAQ;YAAE4E,UAAU,EAAE,WAAW;YAAEjC,OAAO,EAAE;UAAO,CAAE;UAAAZ,QAAA,EACzH8C,MAAM,CAACC,IAAI,CAACnV,UAAU,CAAC,CAAC,CAAC,CAAC,CAACgJ,GAAG,CAACb,GAAG,iBACjCnJ,OAAA;YAAAoT,QAAA,GAAgBjK,GAAG,EAAC,IAAE,EAACmB,IAAI,CAAC8L,SAAS,CAACpV,UAAU,CAAC,CAAC,CAAC,CAACmI,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDzR,OAAA,CAACP,mBAAmB;UAClB4W,IAAI,EAAErV,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjBkV,oBAAoB,EAAGC,YAAY,IAAKpR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmR,YAAY,CAAC3Q,MAAM,CAAE;UAElG3D,gBAAgB,EAAEA,gBAAiB;UACnCsO,YAAY,EAAEpO,kBAAmB;UACjCqU,iBAAiB,EAAE5J,+BAAgC;UACnD6J,iBAAiB,EAAE9J,qBAAsB;UACzC+J,gBAAgB,EAAE3F,mBAAoB;UACtC4F,mBAAmB,EAAEvJ,uBAAwB;UAC7CwJ,cAAc,EAAE5E;QAAmB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDzQ,UAAU,CAAC4E,MAAM,KAAK,CAAC,IAAI,CAACxE,OAAO,iBAClCpB,OAAA,CAACpE,KAAK;UAACgG,QAAQ,EAAC,MAAM;UAACoR,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,EAAC;QAEtC;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzR,OAAA,CAAC3E,GAAG;QAAC2X,EAAE,EAAE;UAAE2C,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBACjBpT,OAAA,CAAC3E,GAAG;UAAC2X,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAER,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE,QAAQ;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACzFpT,OAAA,CAAC1E,UAAU;YAACoY,OAAO,EAAC,IAAI;YAAAN,QAAA,GAAC,aACZ,EAAClS,SAAS,CAAC0E,MAAM,GAAG,CAAC,GAAG,IAAI1E,SAAS,CAAC0E,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA0L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNzR,OAAA,CAACP,mBAAmB;UAClB4W,IAAI,EAAEnV,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjBkV,oBAAoB,EAAGC,YAAY,IAAKpR,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmR,YAAY,CAAC3Q,MAAM,CAAE;UACjG3D,gBAAgB,EAAEA,gBAAiB;UACnCsO,YAAY,EAAElO,iBAAkB;UAChCmU,iBAAiB,EAAE1J,8BAA+B;UAClD2J,iBAAiB,EAAE9J,qBAAsB;UACzC+J,gBAAgB,EAAE3F,mBAAoB;UACtC4F,mBAAmB,EAAEvJ,uBAAwB;UAC7CwJ,cAAc,EAAE5E;QAAmB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDvQ,SAAS,CAAC0E,MAAM,KAAK,CAAC,IAAI,CAACxE,OAAO,iBACjCpB,OAAA,CAACpE,KAAK;UAACgG,QAAQ,EAAC,MAAM;UAACoR,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,EAAC;QAEtC;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLwC,mBAAmB,CAAC,CAAC,eAStBjU,OAAA,CAACN,4BAA4B;QAC3BgC,IAAI,EAAEa,oBAAoB,CAACb,IAAK;QAChCwS,OAAO,EAAEtB,yBAA0B;QACnCnQ,IAAI,EAAEF,oBAAoB,CAACE,IAAK;QAChC7B,UAAU,EAAEA,UAAW;QACvBiW,SAAS,EAAGlV,OAAO,IAAK;UACtB+K,gBAAgB,CAAC/K,OAAO,EAAE,SAAS,CAAC;UACpC;UACA2G,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QACxC,CAAE;QACFqP,OAAO,EAAGnV,OAAO,IAAK;UACpB+K,gBAAgB,CAAC/K,OAAO,EAAE,OAAO,CAAC;QACpC,CAAE;QACFP,OAAO,EAAEmB,oBAAoB,CAACnB;MAAQ;QAAAkQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAEFzR,OAAA,CAACL,4BAA4B;QAC3B+B,IAAI,EAAEkB,oBAAoB,CAAClB,IAAK;QAChCwS,OAAO,EAAErB,yBAA0B;QACnCpQ,IAAI,EAAEG,oBAAoB,CAACH,IAAK;QAChC7B,UAAU,EAAEA,UAAW;QACvBiW,SAAS,EAAEnE,2BAA4B;QACvCoE,OAAO,EAAEnE;MAA0B;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAGFzR,OAAA,CAAC/D,MAAM;QACLyF,IAAI,EAAEgB,kBAAkB,CAAChB,IAAK;QAC9BwS,OAAO,EAAEpB,uBAAwB;QACjCqB,QAAQ,EAAC,IAAI;QACbC,SAAS;QACT2C,oBAAoB,EAAE,KAAM;QAC5BC,WAAW,EAAE,KAAM;QACnBC,aAAa,EAAE,KAAM;QACrBC,iBAAiB,EAAE,IAAK;QACxBC,YAAY,EAAE,KAAM;QACpBC,gBAAgB,EAAE,IAAK;QACvBC,mBAAmB,EAAE,IAAK;QAC1BC,mBAAmB,EAAE,IAAK;QAC1BC,kBAAkB,EAAE,CAAE;QACtBC,eAAe,EAAE;UACfC,OAAO,EAAE,CAAC;UACVC,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,KAAK;UACZC,IAAI,EAAE;QACR,CAAE;QACFC,UAAU,EAAE;UACVC,KAAK,EAAE;YACLC,UAAU,EAAE,MAAM;YAClBC,SAAS,EAAE;UACb;QACF,CAAE;QAAA5E,QAAA,eAEFpT,OAAA,CAAC7D,aAAa;UAAAiX,QAAA,EACX1Q,kBAAkB,CAACD,IAAI,iBACtBzC,OAAA,CAACJ,gBAAgB;YACfgB,UAAU,EAAEA,UAAW;YACvBiB,YAAY,EAAEa,kBAAkB,CAACD,IAAK;YACtCoU,SAAS,EAAGlV,OAAO,IAAK;cACtB,IAAIA,OAAO,EAAE;gBACX+K,gBAAgB,CAAC/K,OAAO,EAAE,SAAS,CAAC;gBACpC;gBACAgB,qBAAqB,CAAC8J,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE/K,IAAI,EAAE;gBAAM,CAAC,CAAC,CAAC;gBACzD;gBACA4G,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;cACxC;cACA;YACF,CAAE;YACFqP,OAAO,EAAGnV,OAAO,IAAK;cACpB+K,gBAAgB,CAAC/K,OAAO,EAAE,OAAO,CAAC;YACpC,CAAE;YACFuS,OAAO,EAAEpB;UAAwB;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGTzR,OAAA,CAACF,kBAAkB;QACjB4B,IAAI,EAAEqB,mBAAmB,CAACrB,IAAK;QAC/BwS,OAAO,EAAErF,wBAAyB;QAClCgI,SAAS,EAAE/H,0BAA2B;QACtClO,UAAU,EAAEA,UAAW;QACvBqX,yBAAyB,EAAElV,mBAAmB,CAACE,WAAY;QAC3DiV,kBAAkB,EAAEnV,mBAAmB,CAACG;MAAgB;QAAAoO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eAGFzR,OAAA,CAAC3E,GAAG;QAAC2X,EAAE,EAAE;UAAEgB,OAAO,EAAE;QAAO,CAAE;QAAAZ,QAAA,eAC3BpT,OAAA,CAACH,0BAA0B;UACzBsY,GAAG,EAAErV,iBAAkB;UACvBlC,UAAU,EAAEA,UAAW;UACvBiW,SAAS,EAAGlV,OAAO,IAAK;YACtB+K,gBAAgB,CAAC/K,OAAO,EAAE,SAAS,CAAC;YACpC;YACA2G,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;UACxC,CAAE;UACFqP,OAAO,EAAGnV,OAAO,IAAK;YACpB+K,gBAAgB,CAAC/K,OAAO,EAAE,OAAO,CAAC;UACpC;QAAE;UAAA2P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzR,OAAA,CAAC/D,MAAM;QACLyF,IAAI,EAAEuN,kBAAkB,CAACvN,IAAK;QAC9BwS,OAAO,EAAEA,CAAA,KAAMhF,qBAAqB,CAAC;UACnCxN,IAAI,EAAE,KAAK;UACXe,IAAI,EAAE,IAAI;UACV0M,OAAO,EAAE,EAAE;UACX/N,OAAO,EAAE,KAAK;UACdkP,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE;QAChB,CAAC,CAAE;QACH4D,QAAQ,EAAC,IAAI;QACbC,SAAS;QAAAhB,QAAA,gBAETpT,OAAA,CAAC9D,WAAW;UAAAkX,QAAA,EACTnE,kBAAkB,CAACqB,UAAU,GAAG,yBAAyB,GAAG;QAAyB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACdzR,OAAA,CAAC7D,aAAa;UAAAiX,QAAA,EACXnE,kBAAkB,CAACxM,IAAI,iBACtBzC,OAAA,CAAC3E,GAAG;YAAC2X,EAAE,EAAE;cAAEoF,EAAE,EAAE;YAAE,CAAE;YAAAhF,QAAA,gBACjBpT,OAAA,CAAC1E,UAAU;cAACoY,OAAO,EAAC,OAAO;cAACV,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACxCpT,OAAA;gBAAAoT,QAAA,EAASnE,kBAAkB,CAACqB,UAAU,GAAG,OAAO,GAAG;cAAO;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KAAC,EAACxC,kBAAkB,CAACxM,IAAI,CAACwK,OAAO;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACbzR,OAAA,CAAC1E,UAAU;cAACoY,OAAO,EAAC,OAAO;cAAC/B,KAAK,EAAC,gBAAgB;cAACqB,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,GAAC,wCAC1B,EAACnE,kBAAkB,CAACqB,UAAU,GAAG,aAAa,GAAG,aAAa,EAAC,GACvG;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZxC,kBAAkB,CAAC7N,OAAO,gBACzBpB,OAAA,CAAC3E,GAAG;cAAC2X,EAAE,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAER,cAAc,EAAE,QAAQ;gBAAE6E,EAAE,EAAE;cAAE,CAAE;cAAAjF,QAAA,eAC5DpT,OAAA,CAACjE,gBAAgB;gBAAAuV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,GACJxC,kBAAkB,CAACE,OAAO,CAACvJ,MAAM,KAAK,CAAC,gBACzC5F,OAAA,CAACpE,KAAK;cAACgG,QAAQ,EAAC,MAAM;cAAAwR,QAAA,EAAC;YAEvB;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAERzR,OAAA,CAAC3E,GAAG;cAAC2X,EAAE,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAE0B,aAAa,EAAE,QAAQ;gBAAEG,GAAG,EAAE;cAAE,CAAE;cAAAzC,QAAA,EAC3DnE,kBAAkB,CAACE,OAAO,CAACnF,GAAG,CAAE8F,OAAO,iBACtC9P,OAAA,CAACxE,MAAM;gBAELkY,OAAO,EAAC,UAAU;gBAClBhC,OAAO,EAAEA,CAAA,KAAMtB,yBAAyB,CAACN,OAAO,CAAE;gBAClDkD,EAAE,EAAE;kBACFQ,cAAc,EAAE,YAAY;kBAC5B8E,SAAS,EAAE,MAAM;kBACjBrF,CAAC,EAAE;gBACL,CAAE;gBAAAG,QAAA,eAEFpT,OAAA,CAAC3E,GAAG;kBAAA+X,QAAA,gBACFpT,OAAA,CAAC1E,UAAU;oBAACoY,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAP,QAAA,EAC1CtD,OAAO,CAACf;kBAAc;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACbzR,OAAA,CAAC1E,UAAU;oBAACoY,OAAO,EAAC,OAAO;oBAAC/B,KAAK,EAAC,gBAAgB;oBAAAyB,QAAA,GAC/CtD,OAAO,CAACyI,YAAY,EAAC,UAAG,EAACzI,OAAO,CAAC0I,YAAY,EAAC,UAAG,EAAC1I,OAAO,CAACC,KAAK;kBAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAhBD3B,OAAO,CAACf,cAAc;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBrB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBzR,OAAA,CAAC5D,aAAa;UAAAgX,QAAA,eACZpT,OAAA,CAACxE,MAAM;YACLkW,OAAO,EAAEA,CAAA,KAAMxC,qBAAqB,CAAC;cACnCxN,IAAI,EAAE,KAAK;cACXe,IAAI,EAAE,IAAI;cACV0M,OAAO,EAAE,EAAE;cACX/N,OAAO,EAAE,KAAK;cACdkP,UAAU,EAAE,KAAK;cACjBC,YAAY,EAAE;YAChB,CAAC,CAAE;YACHkI,QAAQ,EAAExJ,kBAAkB,CAAC7N,OAAQ;YAAAgS,QAAA,EACtC;UAED;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGTzR,OAAA,CAAC/D,MAAM;QACLyF,IAAI,EAAE0N,uBAAuB,CAAC1N,IAAK;QACnCwS,OAAO,EAAEA,CAAA,KAAM7E,0BAA0B,CAAC;UAAE3N,IAAI,EAAE,KAAK;UAAEe,IAAI,EAAE,IAAI;UAAE6M,eAAe,EAAE,IAAI;UAAElO,OAAO,EAAE;QAAM,CAAC,CAAE;QAC9G+S,QAAQ,EAAC,IAAI;QACbC,SAAS;QAAAhB,QAAA,gBAETpT,OAAA,CAAC9D,WAAW;UAAAkX,QAAA,EAAC;QAEb;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdzR,OAAA,CAAC7D,aAAa;UAAAiX,QAAA,EACXhE,uBAAuB,CAAC3M,IAAI,iBAC3BzC,OAAA,CAAC3E,GAAG;YAAC2X,EAAE,EAAE;cAAEoF,EAAE,EAAE;YAAE,CAAE;YAAAhF,QAAA,gBACjBpT,OAAA,CAAC1E,UAAU;cAACoY,OAAO,EAAC,OAAO;cAACV,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACxCpT,OAAA;gBAAAoT,QAAA,EAAQ;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrC,uBAAuB,CAAC3M,IAAI,CAACwK,OAAO;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACbzR,OAAA,CAAC1E,UAAU;cAACoY,OAAO,EAAC,OAAO;cAACV,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACxCpT,OAAA;gBAAAoT,QAAA,EAAQ;cAAQ;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrC,uBAAuB,CAACE,eAAe;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACbzR,OAAA,CAACpE,KAAK;cAACgG,QAAQ,EAAC,SAAS;cAACoR,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,EAAC;YAGzC;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBzR,OAAA,CAAC5D,aAAa;UAAAgX,QAAA,gBACZpT,OAAA,CAACxE,MAAM;YACLkW,OAAO,EAAEA,CAAA,KAAMrC,0BAA0B,CAAC;cAAE3N,IAAI,EAAE,KAAK;cAAEe,IAAI,EAAE,IAAI;cAAE6M,eAAe,EAAE,IAAI;cAAElO,OAAO,EAAE;YAAM,CAAC,CAAE;YAC9GqX,QAAQ,EAAErJ,uBAAuB,CAAChO,OAAQ;YAAAgS,QAAA,EAC3C;UAED;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzR,OAAA,CAACxE,MAAM;YACLkW,OAAO,EAAEhB,8BAA+B;YACxCiB,KAAK,EAAC,SAAS;YACf+B,OAAO,EAAC,WAAW;YACnB+E,QAAQ,EAAErJ,uBAAuB,CAAChO,OAAQ;YAAAgS,QAAA,EAEzChE,uBAAuB,CAAChO,OAAO,gBAAGpB,OAAA,CAACjE,gBAAgB;cAAC6Z,IAAI,EAAE;YAAG;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAS;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGTzR,OAAA,CAAC3D,QAAQ;QACPqF,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxBgX,gBAAgB,EAAE,IAAK;QACvBxE,OAAO,EAAE1H,uBAAwB;QACjCmM,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAzF,QAAA,eAE3DpT,OAAA,CAACpE,KAAK;UAACsY,OAAO,EAAE1H,uBAAwB;UAAC5K,QAAQ,EAAEJ,YAAY,CAACI,QAAS;UAACoR,EAAE,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAC7F5R,YAAY,CAACG;QAAO;UAAA2P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvR,EAAA,CA1gEID,kBAAkB;EAAA,QACYd,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAA4Z,EAAA,GAHxB7Y,kBAAkB;AA4gExB,eAAeA,kBAAkB;AAAC,IAAA6Y,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}