INFO:backend.database:Tentativo di connessione al database: postgresql://postgres:Taranto@localhost:5432/cantieri
INFO:backend.database:Connessione al database riuscita
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\_internal\_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\_internal\_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
INFO:     Started server process [17988]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Stringa di connessione al database: postgresql://postgres:Taranto@localhost:5432/cantieri
Directory temporanea creata: C:\CMS\webapp\static\temp
Configurazione CORS con origini: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8080', 'http://localhost', 'http://localhost:5500', 'http://127.0.0.1:5500', '*']
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:32,775 INFO sqlalchemy.engine.Engine select pg_catalog.version()
INFO:sqlalchemy.engine.Engine:select pg_catalog.version()
2025-06-11 23:04:32,775 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-06-11 23:04:32,776 INFO sqlalchemy.engine.Engine select current_schema()
INFO:sqlalchemy.engine.Engine:select current_schema()
2025-06-11 23:04:32,776 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-06-11 23:04:32,777 INFO sqlalchemy.engine.Engine show standard_conforming_strings
INFO:sqlalchemy.engine.Engine:show standard_conforming_strings
2025-06-11 23:04:32,777 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-06-11 23:04:32,777 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:32,777 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:32,780 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:32,780 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:32,780 INFO sqlalchemy.engine.Engine [generated in 0.00048s] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00048s] {'id_utente_1': 2, 'param_1': 1}
2025-06-11 23:04:32,780 INFO sqlalchemy.engine.Engine [cached since 0.0005949s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.0005949s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Utente a autenticato con successo
Test token riuscito per utente: a, ruolo: user, impersonato: False
Test token riuscito per utente: a, ruolo: user, impersonato: False
2025-06-11 23:04:32,785 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:32,785 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52549 - "POST /api/auth/test-token HTTP/1.1" 200 OK
INFO:     127.0.0.1:52550 - "POST /api/auth/test-token HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:33,343 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:33,344 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:33,344 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:33,344 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:33,345 INFO sqlalchemy.engine.Engine [cached since 0.5648s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.5648s ago] {'id_utente_1': 2, 'param_1': 1}
2025-06-11 23:04:33,345 INFO sqlalchemy.engine.Engine [cached since 0.5649s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.5649s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
Utente a autenticato con successo
2025-06-11 23:04:33,495 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:33,496 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:33,496 INFO sqlalchemy.engine.Engine [cached since 0.716s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.716s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-06-11 23:04:33,692 INFO sqlalchemy.engine.Engine SELECT responsabili.id_responsabile AS responsabili_id_responsabile, responsabili.nome_responsabile AS responsabili_nome_responsabile, responsabili.telefono AS responsabili_telefono, responsabili.email AS responsabili_email, responsabili.id_cantiere AS responsabili_id_cantiere, responsabili.data_creazione AS responsabili_data_creazione, responsabili.attivo AS responsabili_attivo 
FROM responsabili 
WHERE responsabili.id_cantiere = %(id_cantiere_1)s AND responsabili.attivo = true
INFO:sqlalchemy.engine.Engine:SELECT responsabili.id_responsabile AS responsabili_id_responsabile, responsabili.nome_responsabile AS responsabili_nome_responsabile, responsabili.telefono AS responsabili_telefono, responsabili.email AS responsabili_email, responsabili.id_cantiere AS responsabili_id_cantiere, responsabili.data_creazione AS responsabili_data_creazione, responsabili.attivo AS responsabili_attivo 
FROM responsabili 
WHERE responsabili.id_cantiere = %(id_cantiere_1)s AND responsabili.attivo = true
2025-06-11 23:04:33,692 INFO sqlalchemy.engine.Engine [generated in 0.00027s] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00027s] {'id_cantiere_1': 1}
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:33,695 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:33,698 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT responsabili.id_responsabile AS responsabili_id_responsabile, responsabili.nome_responsabile AS responsabili_nome_responsabile, responsabili.telefono AS responsabili_telefono, responsabili.email AS responsabili_email, responsabili.id_cantiere AS responsabili_id_cantiere, responsabili.data_creazione AS responsabili_data_creazione, responsabili.attivo AS responsabili_attivo 
FROM responsabili 
WHERE responsabili.id_cantiere = %(id_cantiere_1)s AND responsabili.attivo = true) AS anon_1
2025-06-11 23:04:33,698 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT responsabili.id_responsabile AS responsabili_id_responsabile, responsabili.nome_responsabile AS responsabili_nome_responsabile, responsabili.telefono AS responsabili_telefono, responsabili.email AS responsabili_email, responsabili.id_cantiere AS responsabili_id_cantiere, responsabili.data_creazione AS responsabili_data_creazione, responsabili.attivo AS responsabili_attivo 
FROM responsabili 
WHERE responsabili.id_cantiere = %(id_cantiere_1)s AND responsabili.attivo = true) AS anon_1
2025-06-11 23:04:33,698 INFO sqlalchemy.engine.Engine [generated in 0.00029s] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:[generated in 0.00029s] {'id_cantiere_1': 1}
INFO:     127.0.0.1:52550 - "GET /api/responsabili/cantiere/1 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52549 - "GET /api/comande/cantiere/1 HTTP/1.1" 200 OK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:33,829 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52553 - "GET /api/comande/cantiere/1/statistiche HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:33,833 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:33,833 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:33,834 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:33,834 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:33,834 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:33,834 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:33,834 INFO sqlalchemy.engine.Engine [cached since 1.054s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 1.054s ago] {'id_utente_1': 2, 'param_1': 1}
2025-06-11 23:04:33,834 INFO sqlalchemy.engine.Engine [cached since 1.055s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 1.055s ago] {'id_utente_1': 2, 'param_1': 1}
2025-06-11 23:04:33,834 INFO sqlalchemy.engine.Engine [cached since 1.055s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 1.055s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
Utente a autenticato con successo
Utente a autenticato con successo
2025-06-11 23:04:33,836 INFO sqlalchemy.engine.Engine SELECT responsabili.id_responsabile AS responsabili_id_responsabile, responsabili.nome_responsabile AS responsabili_nome_responsabile, responsabili.telefono AS responsabili_telefono, responsabili.email AS responsabili_email, responsabili.id_cantiere AS responsabili_id_cantiere, responsabili.data_creazione AS responsabili_data_creazione, responsabili.attivo AS responsabili_attivo 
FROM responsabili 
WHERE responsabili.id_cantiere = %(id_cantiere_1)s AND responsabili.attivo = true
INFO:sqlalchemy.engine.Engine:SELECT responsabili.id_responsabile AS responsabili_id_responsabile, responsabili.nome_responsabile AS responsabili_nome_responsabile, responsabili.telefono AS responsabili_telefono, responsabili.email AS responsabili_email, responsabili.id_cantiere AS responsabili_id_cantiere, responsabili.data_creazione AS responsabili_data_creazione, responsabili.attivo AS responsabili_attivo 
FROM responsabili 
WHERE responsabili.id_cantiere = %(id_cantiere_1)s AND responsabili.attivo = true
2025-06-11 23:04:33,836 INFO sqlalchemy.engine.Engine [cached since 0.1444s ago] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.1444s ago] {'id_cantiere_1': 1}
2025-06-11 23:04:33,981 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT responsabili.id_responsabile AS responsabili_id_responsabile, responsabili.nome_responsabile AS responsabili_nome_responsabile, responsabili.telefono AS responsabili_telefono, responsabili.email AS responsabili_email, responsabili.id_cantiere AS responsabili_id_cantiere, responsabili.data_creazione AS responsabili_data_creazione, responsabili.attivo AS responsabili_attivo 
FROM responsabili 
WHERE responsabili.id_cantiere = %(id_cantiere_1)s AND responsabili.attivo = true) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT responsabili.id_responsabile AS responsabili_id_responsabile, responsabili.nome_responsabile AS responsabili_nome_responsabile, responsabili.telefono AS responsabili_telefono, responsabili.email AS responsabili_email, responsabili.id_cantiere AS responsabili_id_cantiere, responsabili.data_creazione AS responsabili_data_creazione, responsabili.attivo AS responsabili_attivo 
FROM responsabili 
WHERE responsabili.id_cantiere = %(id_cantiere_1)s AND responsabili.attivo = true) AS anon_1
2025-06-11 23:04:33,981 INFO sqlalchemy.engine.Engine [cached since 0.2834s ago] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.2834s ago] {'id_cantiere_1': 1}
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:34,222 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:34,222 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:34,223 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52550 - "GET /api/responsabili/cantiere/1 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52549 - "GET /api/comande/cantiere/1 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52553 - "GET /api/comande/cantiere/1/statistiche HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:34,226 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:34,226 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:34,226 INFO sqlalchemy.engine.Engine [cached since 1.446s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 1.446s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
2025-06-11 23:04:34,479 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52569 - "GET /api/comande/cantiere/1?responsabile=Mario+Rossi HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:34,482 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:34,482 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:34,483 INFO sqlalchemy.engine.Engine [cached since 1.703s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Nuova sessione del database creata
INFO:sqlalchemy.engine.Engine:[cached since 1.703s ago] {'id_utente_1': 2, 'param_1': 1}
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:34,483 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
Utente a autenticato con successo
2025-06-11 23:04:34,484 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:34,484 INFO sqlalchemy.engine.Engine [cached since 1.704s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 1.704s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
2025-06-11 23:04:34,615 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52569 - "GET /api/comande/cantiere/1?responsabile=Mario+Rossi HTTP/1.1" 200 OK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:34,761 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52553 - "GET /api/comande/cantiere/1?responsabile=Francesco+Bianchi HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:34,765 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:34,766 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:34,766 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:34,766 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:34,766 INFO sqlalchemy.engine.Engine [cached since 1.986s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 1.986s ago] {'id_utente_1': 2, 'param_1': 1}
2025-06-11 23:04:34,766 INFO sqlalchemy.engine.Engine [cached since 1.986s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 1.986s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Utente a autenticato con successo
2025-06-11 23:04:34,956 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:34,956 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52569 - "GET /api/comande/cantiere/1?responsabile=Laura+Verdi HTTP/1.1" 200 OK
INFO:     127.0.0.1:52553 - "GET /api/comande/cantiere/1?responsabile=Francesco+Bianchi HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:34,961 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:34,962 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:34,962 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:34,962 INFO sqlalchemy.engine.Engine [cached since 2.183s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 2.183s ago] {'id_utente_1': 2, 'param_1': 1}
2025-06-11 23:04:34,963 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:34,963 INFO sqlalchemy.engine.Engine [cached since 2.183s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 2.183s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Utente a autenticato con successo
2025-06-11 23:04:35,045 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:35,045 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52553 - "GET /api/comande/cantiere/1?responsabile=Marco+Neri HTTP/1.1" 200 OK
INFO:     127.0.0.1:52569 - "GET /api/comande/cantiere/1?responsabile=Laura+Verdi HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:35,050 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:35,050 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:35,050 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:35,050 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:35,051 INFO sqlalchemy.engine.Engine [cached since 2.271s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 2.271s ago] {'id_utente_1': 2, 'param_1': 1}
2025-06-11 23:04:35,051 INFO sqlalchemy.engine.Engine [cached since 2.271s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 2.271s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Utente a autenticato con successo
2025-06-11 23:04:35,134 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:35,135 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52553 - "GET /api/comande/cantiere/1?responsabile=Marco+Neri HTTP/1.1" 200 OK
INFO:     127.0.0.1:52569 - "GET /api/comande/cantiere/1?responsabile=Luca+Gialli HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:35,139 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:35,140 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:35,140 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:35,140 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:35,140 INFO sqlalchemy.engine.Engine [cached since 2.36s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 2.36s ago] {'id_utente_1': 2, 'param_1': 1}
2025-06-11 23:04:35,140 INFO sqlalchemy.engine.Engine [cached since 2.36s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 2.36s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Utente a autenticato con successo
2025-06-11 23:04:35,332 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:04:35,332 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52553 - "GET /api/comande/cantiere/1?responsabile=Sara+Blu HTTP/1.1" 200 OK
INFO:     127.0.0.1:52569 - "GET /api/comande/cantiere/1?responsabile=Luca+Gialli HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:04:35,337 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:04:35,337 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:04:35,337 INFO sqlalchemy.engine.Engine [cached since 2.557s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 2.557s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
2025-06-11 23:04:35,383 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52569 - "GET /api/comande/cantiere/1?responsabile=Sara+Blu HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,531 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,532 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,532 INFO sqlalchemy.engine.Engine [cached since 72.75s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.75s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-06-11 23:05:45,534 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,534 INFO sqlalchemy.engine.Engine [generated in 0.00015s] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00015s] {'id_cantiere_1': 1, 'param_1': 1}
2025-06-11 23:05:45,537 INFO sqlalchemy.engine.Engine 
                SELECT revisione_ufficiale
                FROM cavi
                WHERE id_cantiere = %(cantiere_id)s
                AND revisione_ufficiale IS NOT NULL
                AND revisione_ufficiale != ''
                AND revisione_ufficiale != '00'
                AND revisione_ufficiale != 'TBD'
                ORDER BY revisione_ufficiale DESC
                LIMIT 1
            
INFO:sqlalchemy.engine.Engine:
                SELECT revisione_ufficiale
                FROM cavi
                WHERE id_cantiere = %(cantiere_id)s
                AND revisione_ufficiale IS NOT NULL
                AND revisione_ufficiale != ''
                AND revisione_ufficiale != '00'
                AND revisione_ufficiale != 'TBD'
                ORDER BY revisione_ufficiale DESC
                LIMIT 1
            
2025-06-11 23:05:45,537 INFO sqlalchemy.engine.Engine [generated in 0.00011s] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00011s] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:05:45,538 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/1/revisione-corrente HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,541 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,541 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,541 INFO sqlalchemy.engine.Engine [cached since 72.76s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.76s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-06-11 23:05:45,543 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,543 INFO sqlalchemy.engine.Engine [cached since 0.008438s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.008438s ago] {'id_cantiere_1': 1, 'param_1': 1}
2025-06-11 23:05:45,544 INFO sqlalchemy.engine.Engine 
                SELECT revisione_ufficiale
                FROM cavi
                WHERE id_cantiere = %(cantiere_id)s
                AND revisione_ufficiale IS NOT NULL
                AND revisione_ufficiale != ''
                AND revisione_ufficiale != '00'
                AND revisione_ufficiale != 'TBD'
                ORDER BY revisione_ufficiale DESC
                LIMIT 1
            
INFO:sqlalchemy.engine.Engine:
                SELECT revisione_ufficiale
                FROM cavi
                WHERE id_cantiere = %(cantiere_id)s
                AND revisione_ufficiale IS NOT NULL
                AND revisione_ufficiale != ''
                AND revisione_ufficiale != '00'
                AND revisione_ufficiale != 'TBD'
                ORDER BY revisione_ufficiale DESC
                LIMIT 1
            
2025-06-11 23:05:45,544 INFO sqlalchemy.engine.Engine [cached since 0.007896s ago] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.007896s ago] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:05:45,546 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/1/revisione-corrente HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,548 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,548 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,548 INFO sqlalchemy.engine.Engine [cached since 72.77s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.77s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
API get_cavi chiamata con cantiere_id=1, tipo_cavo=0, stato_installazione=None, tipologia=None, sort_by=None, sort_order=asc, utente=a
Tipo di cantiere_id: <class 'int'>, valore: 1
cantiere_id convertito a intero: 1
2025-06-11 23:05:45,550 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,550 INFO sqlalchemy.engine.Engine [cached since 0.01579s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.01579s ago] {'id_cantiere_1': 1, 'param_1': 1}
Query cantiere eseguita: SELECT * FROM cantieri WHERE id_cantiere = 1
Cantiere trovato: a (ID: 1)
Verifica permessi: utente_id=2, cantiere.id_utente=2, ruolo=user
Esecuzione query cavi per cantiere_id=1
Filtro applicato: cavi attivi (modificato_manualmente != 3 OR modificato_manualmente IS NULL)
Query SQL generata: 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = :cantiere_id
             AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL) ORDER BY id_cavo
2025-06-11 23:05:45,552 INFO sqlalchemy.engine.Engine 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL) ORDER BY id_cavo
INFO:sqlalchemy.engine.Engine:
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL) ORDER BY id_cavo
2025-06-11 23:05:45,552 INFO sqlalchemy.engine.Engine [generated in 0.00012s] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00012s] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
Trovati 96 cavi per il cantiere 1 con filtro tipo_cavo=0
Tipo di dato restituito: <class 'sqlalchemy.engine.row.Row'>
Esempio di riga: ('C001', 1, 'REV1', 'Lighting', 'Signal', 'Grigio', 'LIYCY', '0', '3X2.5MM2+2.5YG', 'N', 'Quadro Secondario', 'QP-92', 'Quadro piano 1', 'Pompa V1', 'UT-97', 'Utenza piano 2', 71.5, 10.0, '', 'C1_B2', 'Installato', 1, datetime.datetime(2025, 6, 5, 22, 49, 43, 567396), 3, 'cantiere', 'cantiere', None, None, None, True)
Nomi delle colonne: RMKeyView(['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina', 'stato_installazione', 'modificato_manualmente', 'timestamp', 'collegamenti', 'responsabile_partenza', 'responsabile_arrivo', 'comanda_posa', 'comanda_partenza', 'comanda_arrivo', 'certificato'])
Convertiti 96 cavi in dizionari
Primi cavi trovati:
  1. ID: C001, Stato: Installato
  2. ID: C002, Stato: Installato
  3. ID: C003, Stato: Installato
  4. ID: C004, Stato: Installato
  5. ID: C005, Stato: Installato
2025-06-11 23:05:45,561 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/1?tipo_cavo=0 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,564 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,564 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,564 INFO sqlalchemy.engine.Engine [cached since 72.78s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.78s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
API get_cavi chiamata con cantiere_id=1, tipo_cavo=0, stato_installazione=None, tipologia=None, sort_by=None, sort_order=asc, utente=a
Tipo di cantiere_id: <class 'int'>, valore: 1
cantiere_id convertito a intero: 1
2025-06-11 23:05:45,566 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,566 INFO sqlalchemy.engine.Engine [cached since 0.03156s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.03156s ago] {'id_cantiere_1': 1, 'param_1': 1}
Query cantiere eseguita: SELECT * FROM cantieri WHERE id_cantiere = 1
Cantiere trovato: a (ID: 1)
Verifica permessi: utente_id=2, cantiere.id_utente=2, ruolo=user
Esecuzione query cavi per cantiere_id=1
Filtro applicato: cavi attivi (modificato_manualmente != 3 OR modificato_manualmente IS NULL)
Query SQL generata: 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = :cantiere_id
             AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL) ORDER BY id_cavo
2025-06-11 23:05:45,566 INFO sqlalchemy.engine.Engine 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL) ORDER BY id_cavo
INFO:sqlalchemy.engine.Engine:
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL) ORDER BY id_cavo
2025-06-11 23:05:45,566 INFO sqlalchemy.engine.Engine [cached since 0.01473s ago] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.01473s ago] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
Trovati 96 cavi per il cantiere 1 con filtro tipo_cavo=0
Tipo di dato restituito: <class 'sqlalchemy.engine.row.Row'>
Esempio di riga: ('C001', 1, 'REV1', 'Lighting', 'Signal', 'Grigio', 'LIYCY', '0', '3X2.5MM2+2.5YG', 'N', 'Quadro Secondario', 'QP-92', 'Quadro piano 1', 'Pompa V1', 'UT-97', 'Utenza piano 2', 71.5, 10.0, '', 'C1_B2', 'Installato', 1, datetime.datetime(2025, 6, 5, 22, 49, 43, 567396), 3, 'cantiere', 'cantiere', None, None, None, True)
Nomi delle colonne: RMKeyView(['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina', 'stato_installazione', 'modificato_manualmente', 'timestamp', 'collegamenti', 'responsabile_partenza', 'responsabile_arrivo', 'comanda_posa', 'comanda_partenza', 'comanda_arrivo', 'certificato'])
Convertiti 96 cavi in dizionari
Primi cavi trovati:
  1. ID: C001, Stato: Installato
  2. ID: C002, Stato: Installato
  3. ID: C003, Stato: Installato
  4. ID: C004, Stato: Installato
  5. ID: C005, Stato: Installato
2025-06-11 23:05:45,575 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/1?tipo_cavo=0 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,578 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,578 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,578 INFO sqlalchemy.engine.Engine [cached since 72.8s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.8s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
2025-06-11 23:05:45,579 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/spare/1 HTTP/1.1" 422 Unprocessable Content
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,581 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,582 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,582 INFO sqlalchemy.engine.Engine [cached since 72.8s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.8s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
2025-06-11 23:05:45,583 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/spare/1 HTTP/1.1" 422 Unprocessable Content
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,585 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,585 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,585 INFO sqlalchemy.engine.Engine [cached since 72.81s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.81s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
API get_cavi chiamata con cantiere_id=1, tipo_cavo=3, stato_installazione=None, tipologia=None, sort_by=None, sort_order=asc, utente=a
Tipo di cantiere_id: <class 'int'>, valore: 1
cantiere_id convertito a intero: 1
2025-06-11 23:05:45,586 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,587 INFO sqlalchemy.engine.Engine [cached since 0.05229s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.05229s ago] {'id_cantiere_1': 1, 'param_1': 1}
Query cantiere eseguita: SELECT * FROM cantieri WHERE id_cantiere = 1
Cantiere trovato: a (ID: 1)
Verifica permessi: utente_id=2, cantiere.id_utente=2, ruolo=user
Esecuzione query cavi per cantiere_id=1
Filtro applicato: cavi spare (modificato_manualmente = 3)
Query SQL generata: 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = :cantiere_id
             AND modificato_manualmente = 3 ORDER BY id_cavo
2025-06-11 23:05:45,587 INFO sqlalchemy.engine.Engine 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             AND modificato_manualmente = 3 ORDER BY id_cavo
INFO:sqlalchemy.engine.Engine:
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             AND modificato_manualmente = 3 ORDER BY id_cavo
2025-06-11 23:05:45,587 INFO sqlalchemy.engine.Engine [generated in 0.00010s] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00010s] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
Trovati 0 cavi per il cantiere 1 con filtro tipo_cavo=3
Nomi delle colonne: RMKeyView(['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina', 'stato_installazione', 'modificato_manualmente', 'timestamp', 'collegamenti', 'responsabile_partenza', 'responsabile_arrivo', 'comanda_posa', 'comanda_partenza', 'comanda_arrivo', 'certificato'])
Convertiti 0 cavi in dizionari
Nessun cavo trovato per questo cantiere con i filtri specificati
2025-06-11 23:05:45,588 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/1?tipo_cavo=3 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,590 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,590 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,590 INFO sqlalchemy.engine.Engine [cached since 72.81s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.81s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
API get_cavi chiamata con cantiere_id=1, tipo_cavo=3, stato_installazione=None, tipologia=None, sort_by=None, sort_order=asc, utente=a
Tipo di cantiere_id: <class 'int'>, valore: 1
cantiere_id convertito a intero: 1
2025-06-11 23:05:45,591 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,591 INFO sqlalchemy.engine.Engine [cached since 0.05721s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.05721s ago] {'id_cantiere_1': 1, 'param_1': 1}
Query cantiere eseguita: SELECT * FROM cantieri WHERE id_cantiere = 1
Cantiere trovato: a (ID: 1)
Verifica permessi: utente_id=2, cantiere.id_utente=2, ruolo=user
Esecuzione query cavi per cantiere_id=1
Filtro applicato: cavi spare (modificato_manualmente = 3)
Query SQL generata: 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = :cantiere_id
             AND modificato_manualmente = 3 ORDER BY id_cavo
2025-06-11 23:05:45,592 INFO sqlalchemy.engine.Engine 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             AND modificato_manualmente = 3 ORDER BY id_cavo
INFO:sqlalchemy.engine.Engine:
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             AND modificato_manualmente = 3 ORDER BY id_cavo
2025-06-11 23:05:45,592 INFO sqlalchemy.engine.Engine [cached since 0.004969s ago] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.004969s ago] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
Trovati 0 cavi per il cantiere 1 con filtro tipo_cavo=3
Nomi delle colonne: RMKeyView(['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina', 'stato_installazione', 'modificato_manualmente', 'timestamp', 'collegamenti', 'responsabile_partenza', 'responsabile_arrivo', 'comanda_posa', 'comanda_partenza', 'comanda_arrivo', 'certificato'])
Convertiti 0 cavi in dizionari
Nessun cavo trovato per questo cantiere con i filtri specificati
2025-06-11 23:05:45,594 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/1?tipo_cavo=3 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,753 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,754 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,754 INFO sqlalchemy.engine.Engine [cached since 72.97s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.97s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
API get_cavi chiamata con cantiere_id=1, tipo_cavo=None, stato_installazione=None, tipologia=None, sort_by=None, sort_order=asc, utente=a
Tipo di cantiere_id: <class 'int'>, valore: 1
cantiere_id convertito a intero: 1
2025-06-11 23:05:45,755 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,755 INFO sqlalchemy.engine.Engine [cached since 0.2208s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.2208s ago] {'id_cantiere_1': 1, 'param_1': 1}
Query cantiere eseguita: SELECT * FROM cantieri WHERE id_cantiere = 1
Cantiere trovato: a (ID: 1)
Verifica permessi: utente_id=2, cantiere.id_utente=2, ruolo=user
Esecuzione query cavi per cantiere_id=1
Query SQL generata: 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = :cantiere_id
             ORDER BY id_cavo
2025-06-11 23:05:45,756 INFO sqlalchemy.engine.Engine 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             ORDER BY id_cavo
INFO:sqlalchemy.engine.Engine:
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             ORDER BY id_cavo
2025-06-11 23:05:45,756 INFO sqlalchemy.engine.Engine [generated in 0.00012s] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00012s] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
Trovati 96 cavi per il cantiere 1 con filtro tipo_cavo=None
Tipo di dato restituito: <class 'sqlalchemy.engine.row.Row'>
Esempio di riga: ('C001', 1, 'REV1', 'Lighting', 'Signal', 'Grigio', 'LIYCY', '0', '3X2.5MM2+2.5YG', 'N', 'Quadro Secondario', 'QP-92', 'Quadro piano 1', 'Pompa V1', 'UT-97', 'Utenza piano 2', 71.5, 10.0, '', 'C1_B2', 'Installato', 1, datetime.datetime(2025, 6, 5, 22, 49, 43, 567396), 3, 'cantiere', 'cantiere', None, None, None, True)
Nomi delle colonne: RMKeyView(['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina', 'stato_installazione', 'modificato_manualmente', 'timestamp', 'collegamenti', 'responsabile_partenza', 'responsabile_arrivo', 'comanda_posa', 'comanda_partenza', 'comanda_arrivo', 'certificato'])
Convertiti 96 cavi in dizionari
Primi cavi trovati:
  1. ID: C001, Stato: Installato
  2. ID: C002, Stato: Installato
  3. ID: C003, Stato: Installato
  4. ID: C004, Stato: Installato
  5. ID: C005, Stato: Installato
2025-06-11 23:05:45,764 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/1 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,767 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,768 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,768 INFO sqlalchemy.engine.Engine [cached since 72.99s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 72.99s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
API get_cavi chiamata con cantiere_id=1, tipo_cavo=None, stato_installazione=None, tipologia=None, sort_by=None, sort_order=asc, utente=a
Tipo di cantiere_id: <class 'int'>, valore: 1
cantiere_id convertito a intero: 1
2025-06-11 23:05:45,769 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,769 INFO sqlalchemy.engine.Engine [cached since 0.235s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.235s ago] {'id_cantiere_1': 1, 'param_1': 1}
Query cantiere eseguita: SELECT * FROM cantieri WHERE id_cantiere = 1
Cantiere trovato: a (ID: 1)
Verifica permessi: utente_id=2, cantiere.id_utente=2, ruolo=user
Esecuzione query cavi per cantiere_id=1
Query SQL generata: 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = :cantiere_id
             ORDER BY id_cavo
2025-06-11 23:05:45,770 INFO sqlalchemy.engine.Engine 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             ORDER BY id_cavo
INFO:sqlalchemy.engine.Engine:
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             ORDER BY id_cavo
2025-06-11 23:05:45,770 INFO sqlalchemy.engine.Engine [cached since 0.01444s ago] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.01444s ago] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
Trovati 96 cavi per il cantiere 1 con filtro tipo_cavo=None
Tipo di dato restituito: <class 'sqlalchemy.engine.row.Row'>
Esempio di riga: ('C001', 1, 'REV1', 'Lighting', 'Signal', 'Grigio', 'LIYCY', '0', '3X2.5MM2+2.5YG', 'N', 'Quadro Secondario', 'QP-92', 'Quadro piano 1', 'Pompa V1', 'UT-97', 'Utenza piano 2', 71.5, 10.0, '', 'C1_B2', 'Installato', 1, datetime.datetime(2025, 6, 5, 22, 49, 43, 567396), 3, 'cantiere', 'cantiere', None, None, None, True)
Nomi delle colonne: RMKeyView(['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina', 'stato_installazione', 'modificato_manualmente', 'timestamp', 'collegamenti', 'responsabile_partenza', 'responsabile_arrivo', 'comanda_posa', 'comanda_partenza', 'comanda_arrivo', 'certificato'])
Convertiti 96 cavi in dizionari
Primi cavi trovati:
  1. ID: C001, Stato: Installato
  2. ID: C002, Stato: Installato
  3. ID: C003, Stato: Installato
  4. ID: C004, Stato: Installato
  5. ID: C005, Stato: Installato
2025-06-11 23:05:45,778 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cavi/1 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,874 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,874 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,874 INFO sqlalchemy.engine.Engine [cached since 73.09s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 73.09s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-06-11 23:05:45,875 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,876 INFO sqlalchemy.engine.Engine [cached since 0.3412s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.3412s ago] {'id_cantiere_1': 1, 'param_1': 1}
2025-06-11 23:05:45,878 INFO sqlalchemy.engine.Engine SELECT certificazionicavi.id_certificazione AS certificazionicavi_id_certificazione, certificazionicavi.id_cavo AS certificazionicavi_id_cavo, certificazionicavi.numero_certificato AS certificazionicavi_numero_certificato, certificazionicavi.data_certificazione AS certificazionicavi_data_certificazione, certificazionicavi.id_operatore AS certificazionicavi_id_operatore, certificazionicavi.valore_isolamento AS certificazionicavi_valore_isolamento, certificazionicavi.strumento_utilizzato AS certificazionicavi_strumento_utilizzato, certificazionicavi.lunghezza_misurata AS certificazionicavi_lunghezza_misurata, cavi.tipologia AS cavo_tipologia, cavi.sezione AS cavo_sezione 
FROM certificazionicavi JOIN cavi ON certificazionicavi.id_cavo = cavi.id_cavo AND certificazionicavi.id_cantiere = cavi.id_cantiere 
WHERE certificazionicavi.id_cantiere = %(id_cantiere_1)s ORDER BY certificazionicavi.data_certificazione DESC
INFO:sqlalchemy.engine.Engine:SELECT certificazionicavi.id_certificazione AS certificazionicavi_id_certificazione, certificazionicavi.id_cavo AS certificazionicavi_id_cavo, certificazionicavi.numero_certificato AS certificazionicavi_numero_certificato, certificazionicavi.data_certificazione AS certificazionicavi_data_certificazione, certificazionicavi.id_operatore AS certificazionicavi_id_operatore, certificazionicavi.valore_isolamento AS certificazionicavi_valore_isolamento, certificazionicavi.strumento_utilizzato AS certificazionicavi_strumento_utilizzato, certificazionicavi.lunghezza_misurata AS certificazionicavi_lunghezza_misurata, cavi.tipologia AS cavo_tipologia, cavi.sezione AS cavo_sezione 
FROM certificazionicavi JOIN cavi ON certificazionicavi.id_cavo = cavi.id_cavo AND certificazionicavi.id_cantiere = cavi.id_cantiere 
WHERE certificazionicavi.id_cantiere = %(id_cantiere_1)s ORDER BY certificazionicavi.data_certificazione DESC
2025-06-11 23:05:45,878 INFO sqlalchemy.engine.Engine [generated in 0.00015s] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00015s] {'id_cantiere_1': 1}
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:05:45,880 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cantieri/1/certificazioni HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,882 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,883 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,883 INFO sqlalchemy.engine.Engine [cached since 73.1s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 73.1s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-06-11 23:05:45,884 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,884 INFO sqlalchemy.engine.Engine [cached since 0.3498s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.3498s ago] {'id_cantiere_1': 1, 'param_1': 1}
2025-06-11 23:05:45,885 INFO sqlalchemy.engine.Engine SELECT certificazionicavi.id_certificazione AS certificazionicavi_id_certificazione, certificazionicavi.id_cavo AS certificazionicavi_id_cavo, certificazionicavi.numero_certificato AS certificazionicavi_numero_certificato, certificazionicavi.data_certificazione AS certificazionicavi_data_certificazione, certificazionicavi.id_operatore AS certificazionicavi_id_operatore, certificazionicavi.valore_isolamento AS certificazionicavi_valore_isolamento, certificazionicavi.strumento_utilizzato AS certificazionicavi_strumento_utilizzato, certificazionicavi.lunghezza_misurata AS certificazionicavi_lunghezza_misurata, cavi.tipologia AS cavo_tipologia, cavi.sezione AS cavo_sezione 
FROM certificazionicavi JOIN cavi ON certificazionicavi.id_cavo = cavi.id_cavo AND certificazionicavi.id_cantiere = cavi.id_cantiere 
WHERE certificazionicavi.id_cantiere = %(id_cantiere_1)s ORDER BY certificazionicavi.data_certificazione DESC
INFO:sqlalchemy.engine.Engine:SELECT certificazionicavi.id_certificazione AS certificazionicavi_id_certificazione, certificazionicavi.id_cavo AS certificazionicavi_id_cavo, certificazionicavi.numero_certificato AS certificazionicavi_numero_certificato, certificazionicavi.data_certificazione AS certificazionicavi_data_certificazione, certificazionicavi.id_operatore AS certificazionicavi_id_operatore, certificazionicavi.valore_isolamento AS certificazionicavi_valore_isolamento, certificazionicavi.strumento_utilizzato AS certificazionicavi_strumento_utilizzato, certificazionicavi.lunghezza_misurata AS certificazionicavi_lunghezza_misurata, cavi.tipologia AS cavo_tipologia, cavi.sezione AS cavo_sezione 
FROM certificazionicavi JOIN cavi ON certificazionicavi.id_cavo = cavi.id_cavo AND certificazionicavi.id_cantiere = cavi.id_cantiere 
WHERE certificazionicavi.id_cantiere = %(id_cantiere_1)s ORDER BY certificazionicavi.data_certificazione DESC
2025-06-11 23:05:45,885 INFO sqlalchemy.engine.Engine [cached since 0.006713s ago] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.006713s ago] {'id_cantiere_1': 1}
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:05:45,886 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cantieri/1/certificazioni HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,914 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,914 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,914 INFO sqlalchemy.engine.Engine [cached since 73.13s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 73.13s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-06-11 23:05:45,915 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,915 INFO sqlalchemy.engine.Engine [cached since 0.3812s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.3812s ago] {'id_cantiere_1': 1, 'param_1': 1}
2025-06-11 23:05:45,917 INFO sqlalchemy.engine.Engine SELECT strumenticertificati.id_strumento AS strumenticertificati_id_strumento, strumenticertificati.id_cantiere AS strumenticertificati_id_cantiere, strumenticertificati.nome AS strumenticertificati_nome, strumenticertificati.marca AS strumenticertificati_marca, strumenticertificati.modello AS strumenticertificati_modello, strumenticertificati.numero_serie AS strumenticertificati_numero_serie, strumenticertificati.data_calibrazione AS strumenticertificati_data_calibrazione, strumenticertificati.data_scadenza_calibrazione AS strumenticertificati_data_scadenza_calibrazione, strumenticertificati.certificato_calibrazione AS strumenticertificati_certificato_calibrazione, strumenticertificati.note AS strumenticertificati_note, strumenticertificati.timestamp_creazione AS strumenticertificati_timestamp_creazione, strumenticertificati.timestamp_modifica AS strumenticertificati_timestamp_modifica, strumenticertificati.tipo_strumento AS strumenticertificati_tipo_strumento, strumenticertificati.ente_certificatore AS strumenticertificati_ente_certificatore, strumenticertificati.range_misura AS strumenticertificati_range_misura, strumenticertificati.precisione AS strumenticertificati_precisione, strumenticertificati.stato_strumento AS strumenticertificati_stato_strumento 
FROM strumenticertificati 
WHERE strumenticertificati.id_cantiere = %(id_cantiere_1)s ORDER BY strumenticertificati.nome, strumenticertificati.marca
INFO:sqlalchemy.engine.Engine:SELECT strumenticertificati.id_strumento AS strumenticertificati_id_strumento, strumenticertificati.id_cantiere AS strumenticertificati_id_cantiere, strumenticertificati.nome AS strumenticertificati_nome, strumenticertificati.marca AS strumenticertificati_marca, strumenticertificati.modello AS strumenticertificati_modello, strumenticertificati.numero_serie AS strumenticertificati_numero_serie, strumenticertificati.data_calibrazione AS strumenticertificati_data_calibrazione, strumenticertificati.data_scadenza_calibrazione AS strumenticertificati_data_scadenza_calibrazione, strumenticertificati.certificato_calibrazione AS strumenticertificati_certificato_calibrazione, strumenticertificati.note AS strumenticertificati_note, strumenticertificati.timestamp_creazione AS strumenticertificati_timestamp_creazione, strumenticertificati.timestamp_modifica AS strumenticertificati_timestamp_modifica, strumenticertificati.tipo_strumento AS strumenticertificati_tipo_strumento, strumenticertificati.ente_certificatore AS strumenticertificati_ente_certificatore, strumenticertificati.range_misura AS strumenticertificati_range_misura, strumenticertificati.precisione AS strumenticertificati_precisione, strumenticertificati.stato_strumento AS strumenticertificati_stato_strumento 
FROM strumenticertificati 
WHERE strumenticertificati.id_cantiere = %(id_cantiere_1)s ORDER BY strumenticertificati.nome, strumenticertificati.marca
2025-06-11 23:05:45,917 INFO sqlalchemy.engine.Engine [generated in 0.00013s] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00013s] {'id_cantiere_1': 1}
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:05:45,920 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cantieri/1/strumenti HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:05:45,923 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:05:45,923 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,923 INFO sqlalchemy.engine.Engine [cached since 73.14s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 73.14s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-06-11 23:05:45,924 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-06-11 23:05:45,924 INFO sqlalchemy.engine.Engine [cached since 0.3899s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.3899s ago] {'id_cantiere_1': 1, 'param_1': 1}
2025-06-11 23:05:45,925 INFO sqlalchemy.engine.Engine SELECT strumenticertificati.id_strumento AS strumenticertificati_id_strumento, strumenticertificati.id_cantiere AS strumenticertificati_id_cantiere, strumenticertificati.nome AS strumenticertificati_nome, strumenticertificati.marca AS strumenticertificati_marca, strumenticertificati.modello AS strumenticertificati_modello, strumenticertificati.numero_serie AS strumenticertificati_numero_serie, strumenticertificati.data_calibrazione AS strumenticertificati_data_calibrazione, strumenticertificati.data_scadenza_calibrazione AS strumenticertificati_data_scadenza_calibrazione, strumenticertificati.certificato_calibrazione AS strumenticertificati_certificato_calibrazione, strumenticertificati.note AS strumenticertificati_note, strumenticertificati.timestamp_creazione AS strumenticertificati_timestamp_creazione, strumenticertificati.timestamp_modifica AS strumenticertificati_timestamp_modifica, strumenticertificati.tipo_strumento AS strumenticertificati_tipo_strumento, strumenticertificati.ente_certificatore AS strumenticertificati_ente_certificatore, strumenticertificati.range_misura AS strumenticertificati_range_misura, strumenticertificati.precisione AS strumenticertificati_precisione, strumenticertificati.stato_strumento AS strumenticertificati_stato_strumento 
FROM strumenticertificati 
WHERE strumenticertificati.id_cantiere = %(id_cantiere_1)s ORDER BY strumenticertificati.nome, strumenticertificati.marca
INFO:sqlalchemy.engine.Engine:SELECT strumenticertificati.id_strumento AS strumenticertificati_id_strumento, strumenticertificati.id_cantiere AS strumenticertificati_id_cantiere, strumenticertificati.nome AS strumenticertificati_nome, strumenticertificati.marca AS strumenticertificati_marca, strumenticertificati.modello AS strumenticertificati_modello, strumenticertificati.numero_serie AS strumenticertificati_numero_serie, strumenticertificati.data_calibrazione AS strumenticertificati_data_calibrazione, strumenticertificati.data_scadenza_calibrazione AS strumenticertificati_data_scadenza_calibrazione, strumenticertificati.certificato_calibrazione AS strumenticertificati_certificato_calibrazione, strumenticertificati.note AS strumenticertificati_note, strumenticertificati.timestamp_creazione AS strumenticertificati_timestamp_creazione, strumenticertificati.timestamp_modifica AS strumenticertificati_timestamp_modifica, strumenticertificati.tipo_strumento AS strumenticertificati_tipo_strumento, strumenticertificati.ente_certificatore AS strumenticertificati_ente_certificatore, strumenticertificati.range_misura AS strumenticertificati_range_misura, strumenticertificati.precisione AS strumenticertificati_precisione, strumenticertificati.stato_strumento AS strumenticertificati_stato_strumento 
FROM strumenticertificati 
WHERE strumenticertificati.id_cantiere = %(id_cantiere_1)s ORDER BY strumenticertificati.nome, strumenticertificati.marca
2025-06-11 23:05:45,925 INFO sqlalchemy.engine.Engine [cached since 0.007617s ago] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.007617s ago] {'id_cantiere_1': 1}
INFO:backend.database:Sessione del database chiusa
2025-06-11 23:05:45,927 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52612 - "GET /api/cantieri/1/strumenti HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:06:22,083 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:06:22,083 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:06:22,083 INFO sqlalchemy.engine.Engine [cached since 109.3s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 109.3s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
2025-06-11 23:06:22,302 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52618 - "GET /api/comande/cantiere/1 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1749676014}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-06-11 23:06:26,218 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:06:26,218 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-06-11 23:06:26,218 INFO sqlalchemy.engine.Engine [cached since 113.4s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 113.4s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
2025-06-11 23:06:26,443 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52618 - "GET /api/comande/cantiere/1 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
ERROR:backend.database:Errore durante l'utilizzo della sessione del database: 401: Credenziali non valide
INFO:backend.database:Sessione del database chiusa
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Errore durante la decodifica del token: Signature has expired.
INFO:     127.0.0.1:52638 - "GET /api/comande/cantiere/1 HTTP/1.1" 401 Unauthorized
INFO:backend.database:Nuova sessione del database creata
2025-06-11 23:09:15,776 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-11 23:09:15,776 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.username = %(username_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.username = %(username_1)s 
 LIMIT %(param_1)s
2025-06-11 23:09:15,776 INFO sqlalchemy.engine.Engine [generated in 0.00012s] {'username_1': 'a', 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00012s] {'username_1': 'a', 'param_1': 1}
ERROR:backend.database:Errore durante l'utilizzo della sessione del database: 401: Username o password non corretti
INFO:backend.database:Sessione del database chiusa
DEBUG - Verifica password per utente: a
DEBUG - Password fornita: 'a'
DEBUG - Password hash nel DB: '$2b$12$6jJ2kk6/oAoNn4CN14H4l.LpPg03ciKSJhCUKWOqoA.wY31ZeOd7i'
DEBUG - Verifica password - Password inserita: 'a'
DEBUG - Hash nel DB (troncato): '$2b$12$6jJ2kk6/oAoNn4CN14H4l.L...'
Errore bcrypt: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
DEBUG - Autenticazione fallita: password non corretta
2025-06-11 23:09:15,978 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52701 - "POST /api/auth/login HTTP/1.1" 401 Unauthorized
