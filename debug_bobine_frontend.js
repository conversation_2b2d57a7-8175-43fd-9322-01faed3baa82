#!/usr/bin/env node
/**
 * Script per debuggare il problema delle bobine sparite nel frontend
 */

const http = require('http');

// Simula una chiamata con token di autenticazione
function testWithAuth(url, token, description) {
    return new Promise((resolve) => {
        console.log(`🧪 Test: ${description}`);
        console.log(`   URL: ${url}`);
        
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || 80,
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            timeout: 10000,
            headers: {
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`,
                'User-Agent': 'Debug-Script'
            }
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   Status: ${res.statusCode}`);
                
                try {
                    const jsonData = JSON.parse(data);
                    
                    if (res.statusCode === 200) {
                        console.log(`   ✅ ${description} - Successo!`);
                        console.log(`   📊 Bobine trovate: ${Array.isArray(jsonData) ? jsonData.length : 'N/A'}`);
                        
                        if (Array.isArray(jsonData) && jsonData.length > 0) {
                            console.log(`   🔍 Prima bobina:`, {
                                id_bobina: jsonData[0].id_bobina,
                                tipologia: jsonData[0].tipologia,
                                metri_residui: jsonData[0].metri_residui,
                                stato_bobina: jsonData[0].stato_bobina
                            });
                        }
                        
                        resolve({ success: true, data: jsonData, status: res.statusCode });
                    } else {
                        console.log(`   ❌ ${description} - Status ${res.statusCode}`);
                        console.log(`   📄 Risposta:`, jsonData);
                        resolve({ success: false, error: jsonData, status: res.statusCode });
                    }
                } catch (e) {
                    console.log(`   ❌ ${description} - Risposta non JSON`);
                    console.log(`   📄 Risposta raw: ${data.substring(0, 200)}...`);
                    resolve({ success: false, error: 'Invalid JSON', status: res.statusCode });
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ ${description} - Errore: ${err.message}`);
            resolve({ success: false, error: err.message });
        });
        
        req.on('timeout', () => {
            console.log(`   ❌ ${description} - Timeout`);
            req.destroy();
            resolve({ success: false, error: 'Timeout' });
        });
        
        req.end();
    });
}

// Test senza autenticazione per confronto
function testWithoutAuth(url, description) {
    return new Promise((resolve) => {
        console.log(`🧪 Test: ${description}`);
        console.log(`   URL: ${url}`);
        
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || 80,
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            timeout: 5000,
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Debug-Script'
            }
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   Status: ${res.statusCode}`);
                console.log(`   📄 Risposta: ${data}`);
                resolve({ success: res.statusCode !== 500, status: res.statusCode });
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ ${description} - Errore: ${err.message}`);
            resolve({ success: false, error: err.message });
        });
        
        req.end();
    });
}

async function main() {
    console.log('🔍 Debug Bobine Frontend - Verifica Caricamento Dati');
    console.log('=' * 70);
    
    // Test 1: Verifica che l'endpoint funzioni senza auth (dovrebbe dare 401)
    console.log('\n📋 FASE 1: Test Endpoint Senza Autenticazione');
    await testWithoutAuth('http://localhost:8001/api/parco-cavi/1', 'Endpoint parco-cavi senza auth');
    
    // Test 2: Prova con un token fittizio (dovrebbe dare 401 o 403)
    console.log('\n📋 FASE 2: Test Endpoint Con Token Fittizio');
    await testWithAuth(
        'http://localhost:8001/api/parco-cavi/1', 
        'fake-token-123', 
        'Endpoint parco-cavi con token fittizio'
    );
    
    // Test 3: Verifica altri endpoint per confronto
    console.log('\n📋 FASE 3: Test Altri Endpoint');
    await testWithoutAuth('http://localhost:8001/api/cantieri/', 'Endpoint cantieri');
    
    console.log('\n' + '=' * 70);
    console.log('📊 DIAGNOSI:');
    console.log('');
    console.log('Se tutti gli endpoint restituiscono 401 "Not authenticated":');
    console.log('✅ Il backend funziona correttamente');
    console.log('❌ Il problema è nell\'autenticazione del frontend');
    console.log('');
    console.log('Se l\'endpoint parco-cavi restituisce 500:');
    console.log('❌ C\'è ancora un problema nel backend');
    console.log('');
    console.log('💡 PROSSIMI PASSI:');
    console.log('1. Verifica il token nel localStorage del browser');
    console.log('2. Controlla la console del browser per errori');
    console.log('3. Verifica che l\'utente sia autenticato correttamente');
    console.log('4. Testa il login se necessario');
}

if (require.main === module) {
    main().catch(console.error);
}
