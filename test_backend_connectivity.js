#!/usr/bin/env node
/**
 * Test di connettività al backend per verificare se l'API parco-cavi è raggiungibile
 */

const http = require('http');
const https = require('https');

function testEndpoint(url, description) {
    return new Promise((resolve) => {
        console.log(`🧪 Test: ${description}`);
        console.log(`   URL: ${url}`);
        
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            timeout: 5000,
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Test-Script'
            }
        };
        
        const req = client.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   Status: ${res.statusCode}`);
                console.log(`   Headers: ${JSON.stringify(res.headers, null, 2)}`);
                
                if (res.statusCode === 200) {
                    console.log(`   ✅ ${description} - OK`);
                    try {
                        const jsonData = JSON.parse(data);
                        console.log(`   📊 Dati ricevuti: ${JSON.stringify(jsonData, null, 2).substring(0, 200)}...`);
                    } catch (e) {
                        console.log(`   📄 Risposta (primi 200 char): ${data.substring(0, 200)}...`);
                    }
                } else {
                    console.log(`   ❌ ${description} - Status ${res.statusCode}`);
                    console.log(`   📄 Risposta: ${data.substring(0, 200)}...`);
                }
                
                resolve({
                    success: res.statusCode === 200,
                    status: res.statusCode,
                    data: data
                });
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ ${description} - Errore: ${err.message}`);
            resolve({
                success: false,
                error: err.message
            });
        });
        
        req.on('timeout', () => {
            console.log(`   ❌ ${description} - Timeout`);
            req.destroy();
            resolve({
                success: false,
                error: 'Timeout'
            });
        });
        
        req.end();
    });
}

async function main() {
    console.log('🚀 Test Connettività Backend - API Parco Cavi');
    console.log('=' * 60);
    
    const tests = [
        {
            url: 'http://localhost:8001/',
            description: 'Backend Root'
        },
        {
            url: 'http://localhost:8001/api/health',
            description: 'Health Check'
        },
        {
            url: 'http://localhost:8001/api/parco-cavi/1',
            description: 'Parco Cavi Cantiere 1'
        },
        {
            url: 'http://localhost:8001/api/parco-cavi/1?disponibili_only=true',
            description: 'Parco Cavi Disponibili Cantiere 1'
        }
    ];
    
    let successCount = 0;
    
    for (const test of tests) {
        const result = await testEndpoint(test.url, test.description);
        if (result.success) {
            successCount++;
        }
        console.log(''); // Riga vuota tra i test
    }
    
    console.log('=' * 60);
    console.log(`📊 Risultati: ${successCount}/${tests.length} test passati`);
    
    if (successCount === 0) {
        console.log('❌ Backend non raggiungibile. Verificare:');
        console.log('   1. Il backend è in esecuzione su porta 8001?');
        console.log('   2. Firewall o antivirus bloccano la connessione?');
        console.log('   3. Il processo backend è crashato?');
        console.log('\n💡 Comandi per avviare il backend:');
        console.log('   cd webapp/backend');
        console.log('   python main.py --port 8001');
    } else if (successCount < tests.length) {
        console.log('⚠️ Backend parzialmente raggiungibile. Alcuni endpoint non funzionano.');
        console.log('   Verificare la configurazione dei router API.');
    } else {
        console.log('✅ Tutti i test passati! Backend completamente funzionante.');
        console.log('   Il problema potrebbe essere nel frontend o nella configurazione axios.');
    }
    
    console.log('\n🔍 Prossimi passi per il debug:');
    console.log('   1. Verificare console browser per errori CORS');
    console.log('   2. Controllare Network tab in DevTools');
    console.log('   3. Verificare token di autenticazione');
    console.log('   4. Testare endpoint con Postman/curl');
}

if (require.main === module) {
    main().catch(console.error);
}
